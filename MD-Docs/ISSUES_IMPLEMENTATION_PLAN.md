# Issues Management System Implementation Plan

## Overview
This plan outlines the complete implementation of the issues management system from the Circle project into the current TTHF project, including database schema migration, hook integration, and backend setup.

## Current State Analysis

### Existing Files
- ✅ `lexorank-utils.ts` - LexoRank utilities for drag-and-drop ordering
- ✅ `SUPABASE_ISSUES_SCHEMA.md` - Complete database schema
- ✅ `useIssues.ts` - React hook for issues management
- ✅ `USEISSUES_DOCUMENTATION.md` - Hook documentation
- ✅ `MIGRATION_EXAMPLE.md` - Migration examples
- ✅ `hooks/use-db.ts` - Existing database hooks

### Integration Points
- Current `use-db.ts` has profile management
- Need to add issues management alongside existing functionality
- Supabase client already configured

## Implementation Phases

### Phase 1: Database Schema Setup

#### 1.1 Drop Existing Conflicting Tables
```sql
-- Drop tables in reverse dependency order
DROP TABLE IF EXISTS issue_labels CASCADE;
DROP TABLE IF EXISTS issues CASCADE;
DROP TABLE IF EXISTS project_teams CASCADE;
DROP TABLE IF EXISTS team_members CASCADE;
DROP TABLE IF EXISTS cycles CASCADE;
DROP TABLE IF EXISTS projects CASCADE;
DROP TABLE IF EXISTS teams CASCADE;
DROP TABLE IF EXISTS labels CASCADE;
DROP TABLE IF EXISTS priorities CASCADE;
DROP TABLE IF EXISTS status CASCADE;
```

#### 1.2 Create New Schema
Execute the complete schema from `SUPABASE_ISSUES_SCHEMA.md`:
- Reference tables (status, priorities, labels)
- Core entities (teams, projects, cycles)
- Junction tables (team_members, project_teams, issue_labels)
- Main issues table with all relationships
- Indexes for performance
- RLS policies for security
- Triggers for updated_at columns

#### 1.3 Insert Sample Data
```sql
-- Status data
INSERT INTO status (id, name, color, icon_name, sort_order) VALUES
('in-progress', 'In Progress', '#facc15', 'InProgressIcon', 1),
('technical-review', 'Technical Review', '#22c55e', 'TechnicalReviewIcon', 2),
('completed', 'Completed', '#8b5cf6', 'CompletedIcon', 3),
('paused', 'Paused', '#0ea5e9', 'PausedIcon', 4),
('to-do', 'Todo', '#f97316', 'ToDoIcon', 5),
('backlog', 'Backlog', '#ec4899', 'BacklogIcon', 6);

-- Priorities data
INSERT INTO priorities (id, name, icon_name, sort_order) VALUES
('no-priority', 'No priority', 'NoPriorityIcon', 1),
('urgent', 'Urgent', 'UrgentPriorityIcon', 2),
('high', 'High', 'HighPriorityIcon', 3),
('medium', 'Medium', 'MediumPriorityIcon', 4),
('low', 'Low', 'LowPriorityIcon', 5);

-- Labels data
INSERT INTO labels (id, name, color) VALUES
('ui', 'UI Enhancement', 'purple'),
('bug', 'Bug', 'red'),
('feature', 'Feature', 'green'),
('documentation', 'Documentation', 'blue'),
('refactor', 'Refactor', 'yellow'),
('performance', 'Performance', 'orange');
```

### Phase 2: Hook Integration

#### 2.1 Update use-db.ts Structure
```typescript
// Add to existing hooks/use-db.ts
export function useIssues() {
  // Implementation from useIssues.ts
}

export function useTeams() {
  // Team management functionality
}

export function useProjects() {
  // Project management functionality
}

export function useLabels() {
  // Label management functionality
}
```

#### 2.2 Type Definitions
```typescript
// Add to lib/supabase/database-modules.ts
export type Issue = Database['public']['Tables']['issues']['Row'] & {
  status: Database['public']['Tables']['status']['Row'];
  assignee: Database['public']['Tables']['profiles']['Row'] | null;
  priority: Database['public']['Tables']['priorities']['Row'];
  project: Database['public']['Tables']['projects']['Row'] | null;
  cycle: Database['public']['Tables']['cycles']['Row'] | null;
  issue_labels: Array<{
    label: Database['public']['Tables']['labels']['Row'];
  }>;
};

export type CreateIssueInput = Database['public']['Tables']['issues']['Insert'];
export type UpdateIssueInput = Database['public']['Tables']['issues']['Update'];
```

### Phase 3: Backend Implementation

#### 3.1 API Routes Structure
```
app/api/
├── issues/
│   ├── route.ts (GET, POST)
│   ├── [id]/
│   │   └── route.ts (GET, PUT, DELETE)
│   ├── search/
│   │   └── route.ts (POST)
│   └── bulk/
│       └── route.ts (PUT, DELETE)
├── teams/
│   └── route.ts (GET, POST)
├── projects/
│   └── route.ts (GET, POST)
└── labels/
    └── route.ts (GET, POST)
```

#### 3.2 Server Actions
```typescript
// lib/actions/issues.ts
export async function createIssue(data: CreateIssueInput) {
  // Server action for creating issues
}

export async function updateIssue(id: string, data: UpdateIssueInput) {
  // Server action for updating issues
}

export async function deleteIssue(id: string) {
  // Server action for deleting issues
}
```

### Phase 4: Real-time Subscriptions

#### 4.1 Supabase Subscriptions Setup
```typescript
// In useIssues hook
useEffect(() => {
  const issuesSubscription = supabase
    .channel('issues-changes')
    .on('postgres_changes', 
      { event: '*', schema: 'public', table: 'issues' },
      handleIssueChange
    )
    .on('postgres_changes',
      { event: '*', schema: 'public', table: 'issue_labels' },
      handleIssueLabelChange
    )
    .subscribe();

  return () => {
    issuesSubscription.unsubscribe();
  };
}, []);
```

### Phase 5: Component Integration

#### 5.1 Update Existing Components
- Replace `useIssuesStore()` with `useIssues()`
- Update import statements
- Add loading and error states
- Handle async operations with try-catch

#### 5.2 New Components Structure
```
components/
├── issues/
│   ├── IssuesList.tsx
│   ├── IssueCard.tsx
│   ├── CreateIssueModal.tsx
│   ├── IssueFilters.tsx
│   └── IssueKanban.tsx
├── teams/
│   └── TeamSelector.tsx
├── projects/
│   └── ProjectSelector.tsx
└── labels/
    └── LabelManager.tsx
```

## Implementation Steps

### Step 1: Database Migration (Priority: High)
1. **Backup existing data** if any conflicting tables exist
2. **Execute drop statements** for conflicting tables
3. **Run complete schema** from SUPABASE_ISSUES_SCHEMA.md
4. **Insert sample data** for testing
5. **Verify RLS policies** are working correctly

### Step 2: Hook Integration (Priority: High)
1. **Copy useIssues implementation** to hooks/use-db.ts
2. **Update imports** to use existing supabase client
3. **Add type definitions** to database-modules.ts
4. **Test basic CRUD operations**

### Step 3: API Layer (Priority: Medium)
1. **Create API routes** for issues management
2. **Implement server actions** for server-side operations
3. **Add validation** and error handling
4. **Test API endpoints**

### Step 4: Real-time Features (Priority: Medium)
1. **Implement Supabase subscriptions**
2. **Handle real-time updates** in components
3. **Test multi-user scenarios**

### Step 5: Component Migration (Priority: Low)
1. **Update existing components** to use new hooks
2. **Add loading states** and error handling
3. **Implement drag-and-drop** with LexoRank
4. **Test user interactions**

## Risk Assessment

### High Risk
- **Data Loss**: Dropping existing tables could lose data
  - *Mitigation*: Backup before migration
- **Breaking Changes**: Existing components might break
  - *Mitigation*: Gradual migration with feature flags

### Medium Risk
- **Performance**: Large datasets might slow queries
  - *Mitigation*: Proper indexing and pagination
- **Real-time**: Subscription conflicts with existing code
  - *Mitigation*: Namespace channels properly

### Low Risk
- **Type Safety**: TypeScript errors during migration
  - *Mitigation*: Update types incrementally

## Testing Strategy

### Unit Tests
- Hook functionality (CRUD operations)
- Utility functions (LexoRank)
- Type definitions

### Integration Tests
- Database operations
- Real-time subscriptions
- API endpoints

### E2E Tests
- Complete issue lifecycle
- Drag-and-drop functionality
- Multi-user scenarios

## Performance Considerations

### Database
- Proper indexing on frequently queried columns
- Pagination for large datasets
- Optimized queries with selective joins

### Frontend
- Optimistic updates for better UX
- Debounced search functionality
- Virtual scrolling for large lists

### Real-time
- Efficient subscription management
- Selective updates to minimize re-renders
- Connection pooling optimization

## Security Considerations

### RLS Policies
- Users can only access issues they're involved with
- Admin users have full access
- Proper team-based access control

### API Security
- Input validation on all endpoints
- Rate limiting for API calls
- Proper authentication checks

## Rollback Plan

### Database Rollback
1. Drop new tables
2. Restore from backup
3. Update application code

### Code Rollback
1. Revert hook changes
2. Restore original components
3. Remove new API routes

## Success Metrics

### Functionality
- ✅ All CRUD operations working
- ✅ Real-time updates functioning
- ✅ Drag-and-drop ordering working
- ✅ Search and filtering operational

### Performance
- ✅ Page load times < 2 seconds
- ✅ Real-time updates < 500ms latency
- ✅ Search results < 1 second

### User Experience
- ✅ No data loss during migration
- ✅ Smooth transitions between states
- ✅ Proper error handling and feedback

## Timeline Estimate

### Phase 1: Database (1-2 days)
- Schema setup: 4 hours
- Data migration: 2 hours
- Testing: 2 hours

### Phase 2: Hooks (2-3 days)
- Implementation: 8 hours
- Testing: 4 hours
- Integration: 4 hours

### Phase 3: API Layer (1-2 days)
- Route creation: 4 hours
- Server actions: 4 hours
- Testing: 2 hours

### Phase 4: Real-time (1 day)
- Subscription setup: 4 hours
- Testing: 2 hours

### Phase 5: Components (2-3 days)
- Migration: 8 hours
- Testing: 4 hours
- Polish: 4 hours

**Total Estimated Time: 7-11 days**

## Next Steps

1. **Review this plan** and approve/modify as needed
2. **Backup existing database** if applicable
3. **Start with Phase 1** (Database Migration)
4. **Test each phase** before proceeding to the next
5. **Document any deviations** from the plan

## Dependencies

### External
- Supabase project access
- Database admin permissions
- LexoRank library

### Internal
- Existing supabase client configuration
- Current authentication system
- Existing component structure

This plan provides a comprehensive roadmap for implementing the issues management system while minimizing risks and ensuring a smooth transition.