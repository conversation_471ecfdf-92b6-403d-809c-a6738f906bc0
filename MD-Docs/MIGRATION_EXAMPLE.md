# Migration Example: From Zustand to useIssues Hook

This document shows how to migrate the existing Circle components from using the Zustand `issues-store.ts` to the new `useIssues` hook with Supabase.

## Before: Using Zustand Store

### Original Component (all-issues.tsx)
```typescript
'use client';

import { status } from '@/mock-data/status';
import { useIssuesStore } from '@/store/issues-store';
import { useSearchStore } from '@/store/search-store';
import { useViewStore } from '@/store/view-store';
import { useFilterStore } from '@/store/filter-store';

export default function AllIssues() {
  const { isSearchOpen, searchQuery } = useSearchStore();
  const { viewType } = useViewStore();
  const { hasActiveFilters } = useFilterStore();
  const { issuesByStatus } = useIssuesStore();

  const isSearching = isSearchOpen && searchQuery.trim() !== '';
  const isViewTypeGrid = viewType === 'grid';
  const isFiltering = hasActiveFilters();

  return (
    <div className={cn('w-full h-full', isViewTypeGrid && 'overflow-x-auto')}>
      {isSearching ? (
        <SearchIssuesView />
      ) : isFiltering ? (
        <FilteredIssuesView isViewTypeGrid={isViewTypeGrid} />
      ) : (
        <GroupIssuesListView isViewTypeGrid={isViewTypeGrid} />
      )}
    </div>
  );
}
```

## After: Using useIssues Hook

### Migrated Component (all-issues.tsx)
```typescript
'use client';

import { status } from '@/mock-data/status';
import { useIssues } from '@/hooks/useIssues';
import { useSearchStore } from '@/store/search-store';
import { useViewStore } from '@/store/view-store';
import { useFilterStore } from '@/store/filter-store';

export default function AllIssues() {
  const { isSearchOpen, searchQuery } = useSearchStore();
  const { viewType } = useViewStore();
  const { hasActiveFilters, filters } = useFilterStore();
  
  // Replace useIssuesStore with useIssues
  const { 
    issuesByStatus, 
    loading, 
    error,
    searchIssues,
    filterIssues 
  } = useIssues();

  const isSearching = isSearchOpen && searchQuery.trim() !== '';
  const isViewTypeGrid = viewType === 'grid';
  const isFiltering = hasActiveFilters();

  // Handle loading and error states
  if (loading) return <div>Loading issues...</div>;
  if (error) return <div>Error: {error}</div>;

  return (
    <div className={cn('w-full h-full', isViewTypeGrid && 'overflow-x-auto')}>
      {isSearching ? (
        <SearchIssuesView searchResults={searchIssues(searchQuery)} />
      ) : isFiltering ? (
        <FilteredIssuesView 
          filteredIssues={filterIssues(filters)}
          isViewTypeGrid={isViewTypeGrid} 
        />
      ) : (
        <GroupIssuesListView 
          issuesByStatus={issuesByStatus}
          isViewTypeGrid={isViewTypeGrid} 
        />
      )}
    </div>
  );
}
```

## Status Selector Migration

### Before: Zustand Store
```typescript
'use client';

import { useIssuesStore } from '@/store/issues-store';
import { status as allStatus, Status } from '@/mock-data/status';

interface StatusSelectorProps {
  status: Status;
  issueId: string;
}

export function StatusSelector({ status, issueId }: StatusSelectorProps) {
  const { updateIssueStatus, filterByStatus } = useIssuesStore();

  const handleStatusChange = (statusId: string) => {
    const newStatus = allStatus.find((s) => s.id === statusId);
    if (newStatus && issueId) {
      updateIssueStatus(issueId, newStatus);
    }
  };

  // ... rest of component
}
```

### After: useIssues Hook
```typescript
'use client';

import { useIssues } from '@/hooks/useIssues';
import { status as allStatus, Status } from '@/mock-data/status';
import { toast } from 'sonner';

interface StatusSelectorProps {
  status: Status;
  issueId: string;
}

export function StatusSelector({ status, issueId }: StatusSelectorProps) {
  const { updateIssueStatus, getIssuesByStatus } = useIssues();

  const handleStatusChange = async (statusId: string) => {
    const newStatus = allStatus.find((s) => s.id === statusId);
    if (newStatus && issueId) {
      try {
        await updateIssueStatus(issueId, newStatus);
        toast.success(`Status updated to ${newStatus.name}`);
      } catch (error) {
        toast.error('Failed to update status');
        console.error('Status update error:', error);
      }
    }
  };

  // ... rest of component (same UI, but with async handling)
}
```

## Create Issue Modal Migration

### Before: Zustand Store
```typescript
import { useIssuesStore } from '@/store/issues-store';
import { useCreateIssueStore } from '@/store/create-issue-store';

export function CreateIssueModal() {
  const { addIssue } = useIssuesStore();
  const { isOpen, closeModal } = useCreateIssueStore();

  const handleSubmit = (issueData: any) => {
    const newIssue = {
      id: uuidv4(),
      identifier: `ISSUE-${Date.now()}`,
      ...issueData,
      createdAt: new Date().toISOString(),
      rank: generateRank(),
    };
    
    addIssue(newIssue);
    closeModal();
    toast.success('Issue created successfully');
  };

  // ... rest of component
}
```

### After: useIssues Hook
```typescript
import { useIssues, CreateIssueInput } from '@/hooks/useIssues';
import { useCreateIssueStore } from '@/store/create-issue-store';
import { IssueRankUtils } from '@/utils/lexorank-utils';

export function CreateIssueModal() {
  const { addIssue, issues } = useIssues();
  const { isOpen, closeModal } = useCreateIssueStore();
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleSubmit = async (issueData: any) => {
    setIsSubmitting(true);
    
    try {
      const newIssueInput: CreateIssueInput = {
        ...issueData,
        rank: IssueRankUtils.getTopRank(issues),
      };
      
      await addIssue(newIssueInput);
      closeModal();
      toast.success('Issue created successfully');
    } catch (error) {
      toast.error('Failed to create issue');
      console.error('Create issue error:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  // ... rest of component with loading state
}
```

## Drag and Drop Migration

### Before: Zustand Store
```typescript
import { useIssuesStore } from '@/store/issues-store';

export function IssueGrid() {
  const { updateIssueStatus } = useIssuesStore();

  const [{ isOver }, drop] = useDrop(() => ({
    accept: IssueDragType,
    drop(item: Issue, monitor) {
      if (monitor.didDrop() && item.status.id !== status.id) {
        updateIssueStatus(item.id, status);
      }
    },
    collect: (monitor) => ({
      isOver: !!monitor.isOver(),
    }),
  }));

  // ... rest of component
}
```

### After: useIssues Hook
```typescript
import { useIssues } from '@/hooks/useIssues';
import { IssueRankUtils } from '@/utils/lexorank-utils';

export function IssueGrid() {
  const { updateIssueStatus, issuesByStatus } = useIssues();

  const [{ isOver }, drop] = useDrop(() => ({
    accept: IssueDragType,
    drop: async (item: Issue, monitor) => {
      if (monitor.didDrop() && item.status_id !== status.id) {
        try {
          // Calculate new rank based on position in target column
          const targetColumnIssues = issuesByStatus[status.id] || [];
          const newRank = IssueRankUtils.getTopRank(targetColumnIssues);
          
          // Update both status and rank
          await updateIssueStatus(item.id, status);
          // Note: You might want to update rank separately or include it in the status update
          
          toast.success('Issue moved successfully');
        } catch (error) {
          toast.error('Failed to move issue');
          console.error('Drag and drop error:', error);
        }
      }
    },
    collect: (monitor) => ({
      isOver: !!monitor.isOver(),
    }),
  }));

  // ... rest of component
}
```

## Search Component Migration

### Before: Zustand Store
```typescript
import { useIssuesStore } from '@/store/issues-store';
import { useSearchStore } from '@/store/search-store';

export function SearchIssues() {
  const [searchResults, setSearchResults] = useState([]);
  const { searchIssues } = useIssuesStore();
  const { searchQuery, isSearchOpen } = useSearchStore();

  useEffect(() => {
    if (searchQuery.trim() === '') {
      setSearchResults([]);
      return;
    }

    const results = searchIssues(searchQuery);
    setSearchResults(results);
  }, [searchQuery, searchIssues]);

  // ... rest of component
}
```

### After: useIssues Hook
```typescript
import { useIssues } from '@/hooks/useIssues';
import { useSearchStore } from '@/store/search-store';

export function SearchIssues() {
  const { searchIssues } = useIssues();
  const { searchQuery, isSearchOpen } = useSearchStore();

  // No need for local state - searchIssues returns results directly
  const searchResults = useMemo(() => {
    if (searchQuery.trim() === '') {
      return [];
    }
    return searchIssues(searchQuery);
  }, [searchQuery, searchIssues]);

  // ... rest of component (same UI)
}
```

## Key Migration Points

### 1. Replace Store Imports
```typescript
// Before
import { useIssuesStore } from '@/store/issues-store';

// After
import { useIssues } from '@/hooks/useIssues';
```

### 2. Handle Async Operations
```typescript
// Before (synchronous)
updateIssueStatus(issueId, newStatus);

// After (asynchronous)
try {
  await updateIssueStatus(issueId, newStatus);
  toast.success('Status updated');
} catch (error) {
  toast.error('Failed to update status');
}
```

### 3. Add Loading States
```typescript
// Before (no loading state needed)
const { issues } = useIssuesStore();

// After (handle loading)
const { issues, loading, error } = useIssues();

if (loading) return <LoadingSpinner />;
if (error) return <ErrorMessage error={error} />;
```

### 4. Use Direct Data Access
```typescript
// Before (store methods)
const results = searchIssues(query);

// After (direct function calls)
const results = searchIssues(query); // Same API, but now it's a direct function
```

### 5. Handle Real-time Updates
The new hook automatically handles real-time updates, so you don't need to manually refresh data. The UI will automatically update when issues change in the database.

### 6. Update Type Imports
```typescript
// Before
import { Issue } from '@/mock-data/issues';

// After
import { Issue, CreateIssueInput } from '@/hooks/useIssues';
```

## Benefits of Migration

1. **Real-time Updates**: Automatic synchronization across all clients
2. **Persistent Data**: Data is stored in Supabase database instead of memory
3. **Better Error Handling**: Proper error states and user feedback
4. **Loading States**: Better UX with loading indicators
5. **Type Safety**: Full TypeScript support with Supabase types
6. **Scalability**: Can handle large datasets with proper pagination
7. **Multi-user Support**: Built-in support for multiple users and permissions

## Testing the Migration

1. **Unit Tests**: Update tests to mock the useIssues hook instead of Zustand store
2. **Integration Tests**: Test the full flow with a test Supabase instance
3. **E2E Tests**: Verify drag-and-drop, real-time updates, and error handling

The migration maintains the same component APIs while adding robust backend integration and real-time capabilities.
