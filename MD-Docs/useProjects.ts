import { useState, useEffect, useCallback } from 'react';
import { createClient } from '@supabase/supabase-js';

// Initialize Supabase client (replace with your actual URL and anon key)
const supabase = createClient(
   process.env.NEXT_PUBLIC_SUPABASE_URL!,
   process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
);

// TypeScript Interfaces
export interface User {
   id: string;
   name: string;
   email: string;
   avatar_url: string | null;
   status: 'online' | 'offline' | 'away';
   role: 'Member' | 'Admin' | 'Guest';
   joined_date: string;
}

export interface Status {
   id: string;
   name: string;
   color: string;
   icon: string;
}

export interface Priority {
   id: string;
   name: string;
   color: string;
   icon: string;
}

export interface HealthStatus {
   id: 'no-update' | 'off-track' | 'on-track' | 'at-risk';
   name: string;
   color: string;
   description: string;
}

export interface Project {
   id: string;
   name: string;
   description: string | null;
   icon: string | null;
   percent_complete: number;
   start_date: string | null;
   target_date: string | null;
   lead_id: string | null;
   status_id: string | null;
   priority_id: string | null;
   health_id: 'no-update' | 'off-track' | 'on-track' | 'at-risk' | null;
   team_id: string | null;
   created_at: string;
   updated_at: string;
   created_by: string;

   // Joined data
   lead?: User;
   status?: Status;
   priority?: Priority;
   health?: HealthStatus;
   member_count?: number;
   issue_count?: number;
}

export interface CreateProjectInput {
   name: string;
   description?: string;
   icon?: string;
   percent_complete?: number;
   start_date?: string;
   target_date?: string;
   lead_id?: string;
   status_id?: string;
   priority_id?: string;
   health_id?: 'no-update' | 'off-track' | 'on-track' | 'at-risk';
   team_id?: string;
}

export interface FilterOptions {
   status?: string[];
   priority?: string[];
   health?: string[];
   lead?: string[];
   team?: string[];
}

interface ProjectsState {
   projects: Project[];
   loading: boolean;
   error: string | null;
}

export function useProjects() {
   const [state, setState] = useState<ProjectsState>({
      projects: [],
      loading: true,
      error: null,
   });

   const [realtimeChannel, setRealtimeChannel] = useState<any>(null);

   // Helper function to build the complete project query with joins
   const buildProjectQuery = useCallback(() => {
      return supabase.from('projects_with_details').select(`
        *,
        lead:profiles!lead_id(id, name, email, avatar_url, status, role, joined_date),
        status:status!status_id(id, name, color, icon),
        priority:priorities!priority_id(id, name, color, icon),
        health:health_status!health_id(id, name, color, description)
      `);
   }, []);

   // Helper function to transform raw database data to our Project interface
   const transformProjectData = useCallback((rawProjects: any[]): Project[] => {
      return rawProjects.map((project) => ({
         id: project.id,
         name: project.name,
         description: project.description,
         icon: project.icon,
         percent_complete: project.percent_complete || 0,
         start_date: project.start_date,
         target_date: project.target_date,
         lead_id: project.lead_id,
         status_id: project.status_id,
         priority_id: project.priority_id,
         health_id: project.health_id,
         team_id: project.team_id,
         created_at: project.created_at,
         updated_at: project.updated_at,
         created_by: project.created_by,
         lead: project.lead,
         status: project.status,
         priority: project.priority,
         health: project.health,
         member_count: project.member_count,
         issue_count: project.issue_count,
      }));
   }, []);

   // Data fetching functions (return data directly)
   const getAllProjects = useCallback((): Project[] => {
      return state.projects;
   }, [state.projects]);

   const getProjectById = useCallback(
      (id: string): Project | undefined => {
         return state.projects.find((project) => project.id === id);
      },
      [state.projects]
   );

   const getProjectsByStatus = useCallback(
      (statusId: string): Project[] => {
         return state.projects.filter((project) => project.status_id === statusId);
      },
      [state.projects]
   );

   const getProjectsByHealth = useCallback(
      (healthId: string): Project[] => {
         return state.projects.filter((project) => project.health_id === healthId);
      },
      [state.projects]
   );

   const getProjectsByLead = useCallback(
      (leadId: string): Project[] => {
         return state.projects.filter((project) => project.lead_id === leadId);
      },
      [state.projects]
   );

   const searchProjects = useCallback(
      (query: string): Project[] => {
         const lowerCaseQuery = query.toLowerCase();
         return state.projects.filter(
            (project) =>
               project.name.toLowerCase().includes(lowerCaseQuery) ||
               (project.description && project.description.toLowerCase().includes(lowerCaseQuery))
         );
      },
      [state.projects]
   );

   const filterProjects = useCallback(
      (filters: FilterOptions): Project[] => {
         return state.projects.filter((project) => {
            if (filters.status && filters.status.length > 0) {
               if (!project.status_id || !filters.status.includes(project.status_id)) {
                  return false;
               }
            }

            if (filters.priority && filters.priority.length > 0) {
               if (!project.priority_id || !filters.priority.includes(project.priority_id)) {
                  return false;
               }
            }

            if (filters.health && filters.health.length > 0) {
               if (!project.health_id || !filters.health.includes(project.health_id)) {
                  return false;
               }
            }

            if (filters.lead && filters.lead.length > 0) {
               if (!project.lead_id || !filters.lead.includes(project.lead_id)) {
                  return false;
               }
            }

            if (filters.team && filters.team.length > 0) {
               if (!project.team_id || !filters.team.includes(project.team_id)) {
                  return false;
               }
            }

            return true;
         });
      },
      [state.projects]
   );

   // Main fetch function
   const fetchProjects = useCallback(async () => {
      try {
         setState((prev) => ({ ...prev, loading: true, error: null }));

         const { data, error } = await buildProjectQuery().order('updated_at', {
            ascending: false,
         });

         if (error) throw error;

         const transformedProjects = transformProjectData(data || []);

         setState((prev) => ({
            ...prev,
            projects: transformedProjects,
            loading: false,
         }));
      } catch (error) {
         setState((prev) => ({
            ...prev,
            loading: false,
            error: error instanceof Error ? error.message : 'Failed to fetch projects',
         }));
      }
   }, [buildProjectQuery, transformProjectData]);

   // Action Functions (return Promise with resolve/reject pattern)
   const addProject = useCallback(
      (projectInput: CreateProjectInput): Promise<Project> => {
         return new Promise(async (resolve, reject) => {
            try {
               // Get current user
               const {
                  data: { user },
               } = await supabase.auth.getUser();
               if (!user) {
                  reject(new Error('User not authenticated'));
                  return;
               }

               const newProject = {
                  ...projectInput,
                  created_by: user.id,
               };

               const { data, error } = await supabase
                  .from('projects')
                  .insert([newProject])
                  .select()
                  .single();

               if (error) throw error;

               // Fetch the complete project data with joins
               const { data: completeProject, error: fetchError } = await buildProjectQuery()
                  .eq('id', data.id)
                  .single();

               if (fetchError) throw fetchError;

               const transformedProject = transformProjectData([completeProject])[0];

               // Optimistic update
               setState((prev) => ({
                  ...prev,
                  projects: [transformedProject, ...prev.projects],
               }));

               resolve(transformedProject);
            } catch (error) {
               reject(error instanceof Error ? error : new Error('Failed to create project'));
            }
         });
      },
      [buildProjectQuery, transformProjectData]
   );

   const updateProject = useCallback(
      (id: string, updates: Partial<Project>): Promise<Project> => {
         return new Promise(async (resolve, reject) => {
            try {
               const { data, error } = await supabase
                  .from('projects')
                  .update(updates)
                  .eq('id', id)
                  .select()
                  .single();

               if (error) throw error;

               // Fetch the complete updated project data
               const { data: completeProject, error: fetchError } = await buildProjectQuery()
                  .eq('id', id)
                  .single();

               if (fetchError) throw fetchError;

               const transformedProject = transformProjectData([completeProject])[0];

               // Optimistic update
               setState((prev) => ({
                  ...prev,
                  projects: prev.projects.map((project) =>
                     project.id === id ? transformedProject : project
                  ),
               }));

               resolve(transformedProject);
            } catch (error) {
               reject(error instanceof Error ? error : new Error('Failed to update project'));
            }
         });
      },
      [buildProjectQuery, transformProjectData]
   );

   const deleteProject = useCallback((id: string): Promise<void> => {
      return new Promise(async (resolve, reject) => {
         try {
            const { error } = await supabase.from('projects').delete().eq('id', id);

            if (error) throw error;

            // Optimistic update
            setState((prev) => ({
               ...prev,
               projects: prev.projects.filter((project) => project.id !== id),
            }));

            resolve();
         } catch (error) {
            reject(error instanceof Error ? error : new Error('Failed to delete project'));
         }
      });
   }, []);

   const updateProjectStatus = useCallback(
      (projectId: string, newStatus: Status): Promise<Project> => {
         return new Promise(async (resolve, reject) => {
            try {
               const { data, error } = await supabase
                  .from('projects')
                  .update({ status_id: newStatus.id })
                  .eq('id', projectId)
                  .select()
                  .single();

               if (error) throw error;

               // Fetch the complete updated project data
               const { data: completeProject, error: fetchError } = await buildProjectQuery()
                  .eq('id', projectId)
                  .single();

               if (fetchError) throw fetchError;

               const transformedProject = transformProjectData([completeProject])[0];

               // Optimistic update
               setState((prev) => ({
                  ...prev,
                  projects: prev.projects.map((project) =>
                     project.id === projectId ? transformedProject : project
                  ),
               }));

               resolve(transformedProject);
            } catch (error) {
               reject(
                  error instanceof Error ? error : new Error('Failed to update project status')
               );
            }
         });
      },
      [buildProjectQuery, transformProjectData]
   );

   const updateProjectPriority = useCallback(
      (projectId: string, newPriority: Priority): Promise<Project> => {
         return new Promise(async (resolve, reject) => {
            try {
               const { data, error } = await supabase
                  .from('projects')
                  .update({ priority_id: newPriority.id })
                  .eq('id', projectId)
                  .select()
                  .single();

               if (error) throw error;

               // Fetch the complete updated project data
               const { data: completeProject, error: fetchError } = await buildProjectQuery()
                  .eq('id', projectId)
                  .single();

               if (fetchError) throw fetchError;

               const transformedProject = transformProjectData([completeProject])[0];

               // Optimistic update
               setState((prev) => ({
                  ...prev,
                  projects: prev.projects.map((project) =>
                     project.id === projectId ? transformedProject : project
                  ),
               }));

               resolve(transformedProject);
            } catch (error) {
               reject(
                  error instanceof Error ? error : new Error('Failed to update project priority')
               );
            }
         });
      },
      [buildProjectQuery, transformProjectData]
   );

   const updateProjectHealth = useCallback(
      (projectId: string, newHealthId: HealthStatus['id']): Promise<Project> => {
         return new Promise(async (resolve, reject) => {
            try {
               const { data, error } = await supabase
                  .from('projects')
                  .update({ health_id: newHealthId })
                  .eq('id', projectId)
                  .select()
                  .single();

               if (error) throw error;

               // Fetch the complete updated project data
               const { data: completeProject, error: fetchError } = await buildProjectQuery()
                  .eq('id', projectId)
                  .single();

               if (fetchError) throw fetchError;

               const transformedProject = transformProjectData([completeProject])[0];

               // Optimistic update
               setState((prev) => ({
                  ...prev,
                  projects: prev.projects.map((project) =>
                     project.id === projectId ? transformedProject : project
                  ),
               }));

               resolve(transformedProject);
            } catch (error) {
               reject(
                  error instanceof Error ? error : new Error('Failed to update project health')
               );
            }
         });
      },
      [buildProjectQuery, transformProjectData]
   );

   const updateProjectLead = useCallback(
      (projectId: string, leadId: string | null): Promise<Project> => {
         return new Promise(async (resolve, reject) => {
            try {
               const { data, error } = await supabase
                  .from('projects')
                  .update({ lead_id: leadId })
                  .eq('id', projectId)
                  .select()
                  .single();

               if (error) throw error;

               // Fetch the complete updated project data
               const { data: completeProject, error: fetchError } = await buildProjectQuery()
                  .eq('id', projectId)
                  .single();

               if (fetchError) throw fetchError;

               const transformedProject = transformProjectData([completeProject])[0];

               // Optimistic update
               setState((prev) => ({
                  ...prev,
                  projects: prev.projects.map((project) =>
                     project.id === projectId ? transformedProject : project
                  ),
               }));

               resolve(transformedProject);
            } catch (error) {
               reject(error instanceof Error ? error : new Error('Failed to update project lead'));
            }
         });
      },
      [buildProjectQuery, transformProjectData]
   );

   const updateProjectProgress = useCallback(
      (projectId: string, percentComplete: number): Promise<Project> => {
         return new Promise(async (resolve, reject) => {
            try {
               // Validate percentage
               if (percentComplete < 0 || percentComplete > 100) {
                  reject(new Error('Percentage must be between 0 and 100'));
                  return;
               }

               const { data, error } = await supabase
                  .from('projects')
                  .update({ percent_complete: percentComplete })
                  .eq('id', projectId)
                  .select()
                  .single();

               if (error) throw error;

               // Fetch the complete updated project data
               const { data: completeProject, error: fetchError } = await buildProjectQuery()
                  .eq('id', projectId)
                  .single();

               if (fetchError) throw fetchError;

               const transformedProject = transformProjectData([completeProject])[0];

               // Optimistic update
               setState((prev) => ({
                  ...prev,
                  projects: prev.projects.map((project) =>
                     project.id === projectId ? transformedProject : project
                  ),
               }));

               resolve(transformedProject);
            } catch (error) {
               reject(
                  error instanceof Error ? error : new Error('Failed to update project progress')
               );
            }
         });
      },
      [buildProjectQuery, transformProjectData]
   );

   const updateProjectDates = useCallback(
      (
         projectId: string,
         startDate: string | null,
         targetDate: string | null
      ): Promise<Project> => {
         return new Promise(async (resolve, reject) => {
            try {
               const { data, error } = await supabase
                  .from('projects')
                  .update({
                     start_date: startDate,
                     target_date: targetDate,
                  })
                  .eq('id', projectId)
                  .select()
                  .single();

               if (error) throw error;

               // Fetch the complete updated project data
               const { data: completeProject, error: fetchError } = await buildProjectQuery()
                  .eq('id', projectId)
                  .single();

               if (fetchError) throw fetchError;

               const transformedProject = transformProjectData([completeProject])[0];

               // Optimistic update
               setState((prev) => ({
                  ...prev,
                  projects: prev.projects.map((project) =>
                     project.id === projectId ? transformedProject : project
                  ),
               }));

               resolve(transformedProject);
            } catch (error) {
               reject(error instanceof Error ? error : new Error('Failed to update project dates'));
            }
         });
      },
      [buildProjectQuery, transformProjectData]
   );

   // Project member management
   const addProjectMember = useCallback(
      (
         projectId: string,
         userId: string,
         role: 'lead' | 'member' | 'viewer' = 'member'
      ): Promise<void> => {
         return new Promise(async (resolve, reject) => {
            try {
               const { error } = await supabase.from('project_members').insert([
                  {
                     project_id: projectId,
                     user_id: userId,
                     role,
                  },
               ]);

               if (error) throw error;

               // Refresh project data to get updated member count
               await fetchProjects();

               resolve();
            } catch (error) {
               reject(error instanceof Error ? error : new Error('Failed to add project member'));
            }
         });
      },
      [fetchProjects]
   );

   const removeProjectMember = useCallback(
      (projectId: string, userId: string): Promise<void> => {
         return new Promise(async (resolve, reject) => {
            try {
               const { error } = await supabase
                  .from('project_members')
                  .delete()
                  .eq('project_id', projectId)
                  .eq('user_id', userId);

               if (error) throw error;

               // Refresh project data to get updated member count
               await fetchProjects();

               resolve();
            } catch (error) {
               reject(
                  error instanceof Error ? error : new Error('Failed to remove project member')
               );
            }
         });
      },
      [fetchProjects]
   );

   // Real-time subscriptions setup
   useEffect(() => {
      fetchProjects();

      // Set up real-time subscription
      const channel = supabase
         .channel('projects-changes')
         .on(
            'postgres_changes',
            {
               event: '*',
               schema: 'public',
               table: 'projects',
            },
            (payload) => {
               console.log('Real-time project change received:', payload);
               // Refetch projects when changes occur
               fetchProjects();
            }
         )
         .on(
            'postgres_changes',
            {
               event: '*',
               schema: 'public',
               table: 'project_members',
            },
            (payload) => {
               console.log('Project members change received:', payload);
               // Refetch projects when member associations change
               fetchProjects();
            }
         )
         .subscribe();

      setRealtimeChannel(channel);

      return () => {
         if (channel) {
            supabase.removeChannel(channel);
         }
      };
   }, [fetchProjects]);

   // Cleanup on unmount
   useEffect(() => {
      return () => {
         if (realtimeChannel) {
            supabase.removeChannel(realtimeChannel);
         }
      };
   }, [realtimeChannel]);

   return {
      // State
      projects: state.projects,
      loading: state.loading,
      error: state.error,

      // Data fetching functions (return data directly)
      getAllProjects,
      getProjectById,
      getProjectsByStatus,
      getProjectsByHealth,
      getProjectsByLead,
      searchProjects,
      filterProjects,

      // Action functions (return Promise with resolve/reject pattern)
      addProject,
      updateProject,
      deleteProject,
      updateProjectStatus,
      updateProjectPriority,
      updateProjectHealth,
      updateProjectLead,
      updateProjectProgress,
      updateProjectDates,
      addProjectMember,
      removeProjectMember,

      // Utility functions
      fetchProjects,
   };
}
