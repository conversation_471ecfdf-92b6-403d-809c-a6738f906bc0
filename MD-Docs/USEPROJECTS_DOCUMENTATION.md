# useProjects Hook Documentation

## Overview
The `useProjects` hook is a comprehensive React hook that provides complete project management functionality for the Circle project management system. It replaces the Zustand-based project store with a Supabase-powered solution that includes real-time updates, optimistic UI updates, and full TypeScript support.

## Features
- **Complete CRUD Operations**: Create, read, update, and delete projects
- **Real-time Updates**: Live synchronization across all clients using Supabase subscriptions
- **Optimistic Updates**: Immediate UI feedback with automatic rollback on errors
- **Project Health Management**: Track project health status (on-track, off-track, at-risk, no-update)
- **Lead Assignment**: Assign and manage project leads
- **Progress Tracking**: Monitor project completion percentage
- **Date Management**: Handle start dates and target dates
- **Member Management**: Add and remove project members
- **Advanced Filtering**: Filter projects by status, priority, health, lead, and team
- **Search Functionality**: Search projects by name and description
- **Error Handling**: Comprehensive error handling with user-friendly messages
- **Loading States**: Built-in loading states for better UX

## Installation

```bash
npm install @supabase/supabase-js
```

## Setup

1. **Environment Variables**: Add your Supabase credentials to your environment:
```env
NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
```

2. **Database Setup**: Run the SQL scripts from `SUPABASE_PROJECTS_SCHEMA.md` to set up your database tables and policies.

## Basic Usage

```typescript
import { useProjects } from './useProjects';

function ProjectsComponent() {
  const {
    projects,
    loading,
    error,
    getAllProjects,
    addProject,
    updateProject,
    deleteProject,
  } = useProjects();

  if (loading) return <div>Loading projects...</div>;
  if (error) return <div>Error: {error}</div>;

  const allProjects = getAllProjects();

  return (
    <div>
      {allProjects.map(project => (
        <div key={project.id}>
          <h3>{project.name}</h3>
          <p>Progress: {project.percent_complete}%</p>
        </div>
      ))}
    </div>
  );
}
```

## API Reference

### State Properties

#### `projects: Project[]`
Array of all projects with complete data including joined relationships.

#### `loading: boolean`
Indicates whether the initial data fetch is in progress.

#### `error: string | null`
Contains error message if any operation fails, null otherwise.

### Data Fetching Functions
These functions return data directly and are safe to call in render functions.

#### `getAllProjects(): Project[]`
Returns all projects in the current state.

#### `getProjectById(id: string): Project | undefined`
Returns a specific project by ID.

#### `getProjectsByStatus(statusId: string): Project[]`
Returns all projects with the specified status.

#### `getProjectsByHealth(healthId: string): Project[]`
Returns all projects with the specified health status.

#### `getProjectsByLead(leadId: string): Project[]`
Returns all projects led by the specified user.

#### `searchProjects(query: string): Project[]`
Searches projects by name and description (case-insensitive).

#### `filterProjects(filters: FilterOptions): Project[]`
Filters projects based on multiple criteria:
```typescript
interface FilterOptions {
  status?: string[];
  priority?: string[];
  health?: string[];
  lead?: string[];
  team?: string[];
}
```

### Action Functions
These functions return Promises and should be used with async/await or .then()/.catch().

#### `addProject(projectInput: CreateProjectInput): Promise<Project>`
Creates a new project.

```typescript
interface CreateProjectInput {
  name: string;
  description?: string;
  icon?: string;
  percent_complete?: number;
  start_date?: string;
  target_date?: string;
  lead_id?: string;
  status_id?: string;
  priority_id?: string;
  health_id?: 'no-update' | 'off-track' | 'on-track' | 'at-risk';
  team_id?: string;
}
```

**Example:**
```typescript
const handleCreateProject = async () => {
  try {
    const newProject = await addProject({
      name: 'New Project',
      description: 'Project description',
      health_id: 'on-track',
      percent_complete: 0,
    });
    console.log('Project created:', newProject);
  } catch (error) {
    console.error('Failed to create project:', error);
  }
};
```

#### `updateProject(id: string, updates: Partial<Project>): Promise<Project>`
Updates an existing project with partial data.

**Example:**
```typescript
const handleUpdateProject = async (projectId: string) => {
  try {
    const updatedProject = await updateProject(projectId, {
      name: 'Updated Project Name',
      percent_complete: 75,
    });
    console.log('Project updated:', updatedProject);
  } catch (error) {
    console.error('Failed to update project:', error);
  }
};
```

#### `deleteProject(id: string): Promise<void>`
Deletes a project permanently.

**Example:**
```typescript
const handleDeleteProject = async (projectId: string) => {
  try {
    await deleteProject(projectId);
    console.log('Project deleted successfully');
  } catch (error) {
    console.error('Failed to delete project:', error);
  }
};
```

#### `updateProjectStatus(projectId: string, newStatus: Status): Promise<Project>`
Updates the status of a project.

#### `updateProjectPriority(projectId: string, newPriority: Priority): Promise<Project>`
Updates the priority of a project.

#### `updateProjectHealth(projectId: string, newHealthId: HealthStatus['id']): Promise<Project>`
Updates the health status of a project.

**Example:**
```typescript
const handleHealthUpdate = async (projectId: string) => {
  try {
    const updatedProject = await updateProjectHealth(projectId, 'at-risk');
    console.log('Project health updated:', updatedProject);
  } catch (error) {
    console.error('Failed to update project health:', error);
  }
};
```

#### `updateProjectLead(projectId: string, leadId: string | null): Promise<Project>`
Assigns or removes a project lead.

#### `updateProjectProgress(projectId: string, percentComplete: number): Promise<Project>`
Updates the completion percentage (0-100).

**Example:**
```typescript
const handleProgressUpdate = async (projectId: string, progress: number) => {
  try {
    const updatedProject = await updateProjectProgress(projectId, progress);
    console.log('Progress updated:', updatedProject);
  } catch (error) {
    console.error('Failed to update progress:', error);
  }
};
```

#### `updateProjectDates(projectId: string, startDate: string | null, targetDate: string | null): Promise<Project>`
Updates project start and target dates.

#### `addProjectMember(projectId: string, userId: string, role?: 'lead' | 'member' | 'viewer'): Promise<void>`
Adds a member to a project with specified role (defaults to 'member').

#### `removeProjectMember(projectId: string, userId: string): Promise<void>`
Removes a member from a project.

### Utility Functions

#### `fetchProjects(): Promise<void>`
Manually refetches all projects from the database. Usually not needed as the hook handles this automatically.

## TypeScript Interfaces

### Project Interface
```typescript
interface Project {
  id: string;
  name: string;
  description: string | null;
  icon: string | null;
  percent_complete: number;
  start_date: string | null;
  target_date: string | null;
  lead_id: string | null;
  status_id: string | null;
  priority_id: string | null;
  health_id: 'no-update' | 'off-track' | 'on-track' | 'at-risk' | null;
  team_id: string | null;
  created_at: string;
  updated_at: string;
  created_by: string;
  
  // Joined data
  lead?: User;
  status?: Status;
  priority?: Priority;
  health?: HealthStatus;
  member_count?: number;
  issue_count?: number;
}
```

### Supporting Interfaces
```typescript
interface User {
  id: string;
  name: string;
  email: string;
  avatar_url: string | null;
  status: 'online' | 'offline' | 'away';
  role: 'Member' | 'Admin' | 'Guest';
  joined_date: string;
}

interface Status {
  id: string;
  name: string;
  color: string;
  icon: string;
}

interface Priority {
  id: string;
  name: string;
  color: string;
  icon: string;
}

interface HealthStatus {
  id: 'no-update' | 'off-track' | 'on-track' | 'at-risk';
  name: string;
  color: string;
  description: string;
}
```

## Real-time Updates

The hook automatically subscribes to real-time changes on the `projects` and `project_members` tables. When changes occur:

1. **Automatic Refetch**: The hook automatically refetches all projects to ensure data consistency
2. **Console Logging**: Changes are logged to the console for debugging
3. **Optimistic Updates**: UI updates immediately, with automatic rollback on errors

## Error Handling

All action functions include comprehensive error handling:

- **Network Errors**: Handled gracefully with user-friendly messages
- **Validation Errors**: Input validation with specific error messages
- **Permission Errors**: RLS policy violations are caught and reported
- **Optimistic Rollback**: Failed operations automatically revert optimistic updates

## Performance Considerations

- **Memoized Functions**: All functions are wrapped with `useCallback` for optimal performance
- **Efficient Queries**: Database queries use indexes and joins for fast data retrieval
- **Real-time Optimization**: Subscriptions are properly cleaned up to prevent memory leaks
- **Optimistic Updates**: Immediate UI feedback reduces perceived latency

## Migration from Zustand

See `PROJECT_MIGRATION_EXAMPLE.md` for detailed migration examples and patterns.

## Best Practices

1. **Error Handling**: Always wrap action functions in try-catch blocks
2. **Loading States**: Use the `loading` state to show appropriate UI feedback
3. **Optimistic Updates**: Trust the optimistic updates but handle errors gracefully
4. **Real-time**: Let the hook handle real-time updates automatically
5. **Performance**: Use the data fetching functions liberally - they're optimized for performance

## Troubleshooting

### Common Issues

1. **"User not authenticated" Error**: Ensure user is logged in before calling action functions
2. **RLS Policy Violations**: Check that the user has appropriate permissions for the operation
3. **Real-time Not Working**: Verify Supabase real-time is enabled for your project
4. **Stale Data**: The hook should handle this automatically, but you can call `fetchProjects()` manually if needed

### Debug Mode

Enable console logging by checking the browser console for real-time update messages and error details.
