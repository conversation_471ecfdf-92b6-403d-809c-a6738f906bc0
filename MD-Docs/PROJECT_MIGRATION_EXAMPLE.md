# Project Migration Example: From Zustand to useProjects Hook

## Overview
This document provides comprehensive examples for migrating from the Zustand-based project store to the new `useProjects` Supabase hook. It includes before/after code examples for all major project components.

## Migration Strategy

### 1. Replace Store Import
**Before (Zustand):**
```typescript
import { useIssuesStore } from '@/store/issues-store';
```

**After (Supabase Hook):**
```typescript
import { useProjects } from '@/hooks/useProjects';
```

### 2. Update Component Logic
Replace Zustand store calls with hook functions and handle async operations properly.

## Component Migrations

### 1. Projects List Component

**Before (components/common/projects/projects.tsx):**
```typescript
'use client';

import { projects } from '@/mock-data/projects';
import ProjectLine from './project-line';

export default function Projects() {
   return (
      <div className="w-full">
         <div className="bg-container px-6 py-1.5 text-sm flex items-center text-muted-foreground border-b sticky top-0 z-10">
            <div className="w-[60%] sm:w-[70%] xl:w-[46%]">Title</div>
            <div className="w-[20%] sm:w-[10%] xl:w-[13%] pl-2.5">Health</div>
            <div className="hidden w-[10%] sm:block pl-2">Priority</div>
            <div className="hidden xl:block xl:w-[13%] pl-2">Lead</div>
            <div className="hidden xl:block xl:w-[13%] pl-2.5">Target date</div>
            <div className="w-[20%] sm:w-[10%] pl-2">Status</div>
         </div>

         <div className="w-full">
            {projects.map((project) => (
               <ProjectLine key={project.id} project={project} />
            ))}
         </div>
      </div>
   );
}
```

**After (with useProjects hook):**
```typescript
'use client';

import { useProjects } from '@/hooks/useProjects';
import ProjectLine from './project-line';

export default function Projects() {
   const { projects, loading, error, getAllProjects } = useProjects();

   if (loading) {
      return (
         <div className="w-full flex items-center justify-center py-8">
            <div className="text-muted-foreground">Loading projects...</div>
         </div>
      );
   }

   if (error) {
      return (
         <div className="w-full flex items-center justify-center py-8">
            <div className="text-red-500">Error loading projects: {error}</div>
         </div>
      );
   }

   const allProjects = getAllProjects();

   return (
      <div className="w-full">
         <div className="bg-container px-6 py-1.5 text-sm flex items-center text-muted-foreground border-b sticky top-0 z-10">
            <div className="w-[60%] sm:w-[70%] xl:w-[46%]">Title</div>
            <div className="w-[20%] sm:w-[10%] xl:w-[13%] pl-2.5">Health</div>
            <div className="hidden w-[10%] sm:block pl-2">Priority</div>
            <div className="hidden xl:block xl:w-[13%] pl-2">Lead</div>
            <div className="hidden xl:block xl:w-[13%] pl-2.5">Target date</div>
            <div className="w-[20%] sm:w-[10%] pl-2">Status</div>
         </div>

         <div className="w-full">
            {allProjects.map((project) => (
               <ProjectLine key={project.id} project={project} />
            ))}
         </div>
      </div>
   );
}
```

### 2. Health Popover Component

**Before (components/common/projects/health-popover.tsx):**
```typescript
'use client';

import { Button } from '@/components/ui/button';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Project } from '@/mock-data/projects';
import { healthStatuses } from '@/mock-data/health-status';
import { CheckIcon } from 'lucide-react';
import { useState } from 'react';

interface HealthPopoverProps {
   project: Project;
}

export function HealthPopover({ project }: HealthPopoverProps) {
   const [open, setOpen] = useState(false);
   const currentHealth = healthStatuses.find(h => h.id === project.healthId);

   const handleHealthChange = (healthId: string) => {
      // Mock update - in real app this would update the store
      console.log('Updating project health:', project.id, healthId);
      setOpen(false);
   };

   return (
      <Popover open={open} onOpenChange={setOpen}>
         <PopoverTrigger asChild>
            <Button
               variant="ghost"
               size="xs"
               className="h-6 px-2 text-xs font-medium justify-start"
               style={{ color: currentHealth?.color }}
            >
               {currentHealth?.name || 'No Update'}
            </Button>
         </PopoverTrigger>
         <PopoverContent className="w-48 p-1" align="start">
            {healthStatuses.map((health) => (
               <Button
                  key={health.id}
                  variant="ghost"
                  size="sm"
                  className="w-full justify-between text-xs"
                  onClick={() => handleHealthChange(health.id)}
               >
                  <span style={{ color: health.color }}>{health.name}</span>
                  {project.healthId === health.id && <CheckIcon size={14} />}
               </Button>
            ))}
         </PopoverContent>
      </Popover>
   );
}
```

**After (with useProjects hook):**
```typescript
'use client';

import { Button } from '@/components/ui/button';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { useProjects, Project, HealthStatus } from '@/hooks/useProjects';
import { CheckIcon } from 'lucide-react';
import { useState } from 'react';
import { toast } from 'sonner';

interface HealthPopoverProps {
   project: Project;
}

const healthStatuses: HealthStatus[] = [
   { id: 'no-update', name: 'No Update', color: '#6B7280', description: 'No recent updates' },
   { id: 'off-track', name: 'Off Track', color: '#EF4444', description: 'Project is behind schedule' },
   { id: 'on-track', name: 'On Track', color: '#10B981', description: 'Project is on schedule' },
   { id: 'at-risk', name: 'At Risk', color: '#F59E0B', description: 'Project may face delays' },
];

export function HealthPopover({ project }: HealthPopoverProps) {
   const [open, setOpen] = useState(false);
   const { updateProjectHealth } = useProjects();
   const currentHealth = healthStatuses.find(h => h.id === project.health_id);

   const handleHealthChange = async (healthId: HealthStatus['id']) => {
      try {
         await updateProjectHealth(project.id, healthId);
         toast.success('Project health updated successfully');
         setOpen(false);
      } catch (error) {
         toast.error('Failed to update project health');
         console.error('Error updating project health:', error);
      }
   };

   return (
      <Popover open={open} onOpenChange={setOpen}>
         <PopoverTrigger asChild>
            <Button
               variant="ghost"
               size="xs"
               className="h-6 px-2 text-xs font-medium justify-start"
               style={{ color: currentHealth?.color }}
            >
               {currentHealth?.name || 'No Update'}
            </Button>
         </PopoverTrigger>
         <PopoverContent className="w-48 p-1" align="start">
            {healthStatuses.map((health) => (
               <Button
                  key={health.id}
                  variant="ghost"
                  size="sm"
                  className="w-full justify-between text-xs"
                  onClick={() => handleHealthChange(health.id)}
               >
                  <span style={{ color: health.color }}>{health.name}</span>
                  {project.health_id === health.id && <CheckIcon size={14} />}
               </Button>
            ))}
         </PopoverContent>
      </Popover>
   );
}
```

### 3. Lead Selector Component

**Before (components/common/projects/lead-selector.tsx):**
```typescript
'use client';

import { Button } from '@/components/ui/button';
import { Command, CommandEmpty, CommandGroup, CommandInput, CommandItem, CommandList } from '@/components/ui/command';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { User } from '@/mock-data/users';
import { users } from '@/mock-data/users';
import { CheckIcon, UserIcon } from 'lucide-react';
import { useState } from 'react';

interface LeadSelectorProps {
   lead: User | undefined;
}

export function LeadSelector({ lead }: LeadSelectorProps) {
   const [open, setOpen] = useState(false);

   const handleLeadChange = (userId: string | null) => {
      // Mock update - in real app this would update the store
      console.log('Updating project lead:', userId);
      setOpen(false);
   };

   return (
      <Popover open={open} onOpenChange={setOpen}>
         <PopoverTrigger asChild>
            <Button variant="ghost" size="xs" className="h-6 px-2 text-xs justify-start">
               {lead ? (
                  <div className="flex items-center gap-1.5">
                     <Avatar className="size-4">
                        <AvatarImage src={lead.avatar} />
                        <AvatarFallback className="text-[10px]">
                           {lead.name.split(' ').map(n => n[0]).join('')}
                        </AvatarFallback>
                     </Avatar>
                     <span>{lead.name}</span>
                  </div>
               ) : (
                  <div className="flex items-center gap-1.5">
                     <UserIcon className="size-4" />
                     <span>No Lead</span>
                  </div>
               )}
            </Button>
         </PopoverTrigger>
         <PopoverContent className="w-64 p-1" align="start">
            <Command>
               <CommandInput placeholder="Search users..." />
               <CommandList>
                  <CommandEmpty>No users found.</CommandEmpty>
                  <CommandGroup>
                     <CommandItem onSelect={() => handleLeadChange(null)}>
                        <div className="flex items-center gap-2">
                           <UserIcon className="size-4" />
                           No Lead
                        </div>
                        {!lead && <CheckIcon size={14} className="ml-auto" />}
                     </CommandItem>
                     {users.map((user) => (
                        <CommandItem key={user.id} onSelect={() => handleLeadChange(user.id)}>
                           <div className="flex items-center gap-2">
                              <Avatar className="size-4">
                                 <AvatarImage src={user.avatar} />
                                 <AvatarFallback className="text-[10px]">
                                    {user.name.split(' ').map(n => n[0]).join('')}
                                 </AvatarFallback>
                              </Avatar>
                              {user.name}
                           </div>
                           {lead?.id === user.id && <CheckIcon size={14} className="ml-auto" />}
                        </CommandItem>
                     ))}
                  </CommandGroup>
               </CommandList>
            </Command>
         </PopoverContent>
      </Popover>
   );
}
```

**After (with useProjects hook):**
```typescript
'use client';

import { Button } from '@/components/ui/button';
import { Command, CommandEmpty, CommandGroup, CommandInput, CommandItem, CommandList } from '@/components/ui/command';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { useProjects, User, Project } from '@/hooks/useProjects';
import { CheckIcon, UserIcon } from 'lucide-react';
import { useState, useEffect } from 'react';
import { toast } from 'sonner';

interface LeadSelectorProps {
   project: Project;
   availableUsers: User[]; // Pass this as prop or fetch from another hook
}

export function LeadSelector({ project, availableUsers }: LeadSelectorProps) {
   const [open, setOpen] = useState(false);
   const { updateProjectLead } = useProjects();

   const handleLeadChange = async (userId: string | null) => {
      try {
         await updateProjectLead(project.id, userId);
         toast.success('Project lead updated successfully');
         setOpen(false);
      } catch (error) {
         toast.error('Failed to update project lead');
         console.error('Error updating project lead:', error);
      }
   };

   return (
      <Popover open={open} onOpenChange={setOpen}>
         <PopoverTrigger asChild>
            <Button variant="ghost" size="xs" className="h-6 px-2 text-xs justify-start">
               {project.lead ? (
                  <div className="flex items-center gap-1.5">
                     <Avatar className="size-4">
                        <AvatarImage src={project.lead.avatar_url || ''} />
                        <AvatarFallback className="text-[10px]">
                           {project.lead.name.split(' ').map(n => n[0]).join('')}
                        </AvatarFallback>
                     </Avatar>
                     <span>{project.lead.name}</span>
                  </div>
               ) : (
                  <div className="flex items-center gap-1.5">
                     <UserIcon className="size-4" />
                     <span>No Lead</span>
                  </div>
               )}
            </Button>
         </PopoverTrigger>
         <PopoverContent className="w-64 p-1" align="start">
            <Command>
               <CommandInput placeholder="Search users..." />
               <CommandList>
                  <CommandEmpty>No users found.</CommandEmpty>
                  <CommandGroup>
                     <CommandItem onSelect={() => handleLeadChange(null)}>
                        <div className="flex items-center gap-2">
                           <UserIcon className="size-4" />
                           No Lead
                        </div>
                        {!project.lead && <CheckIcon size={14} className="ml-auto" />}
                     </CommandItem>
                     {availableUsers.map((user) => (
                        <CommandItem key={user.id} onSelect={() => handleLeadChange(user.id)}>
                           <div className="flex items-center gap-2">
                              <Avatar className="size-4">
                                 <AvatarImage src={user.avatar_url || ''} />
                                 <AvatarFallback className="text-[10px]">
                                    {user.name.split(' ').map(n => n[0]).join('')}
                                 </AvatarFallback>
                              </Avatar>
                              {user.name}
                           </div>
                           {project.lead?.id === user.id && <CheckIcon size={14} className="ml-auto" />}
                        </CommandItem>
                     ))}
                  </CommandGroup>
               </CommandList>
            </Command>
         </PopoverContent>
      </Popover>
   );
}
```

### 4. Status with Percent Component

**Before (components/common/projects/status-with-percent.tsx):**
```typescript
'use client';

import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Status } from '@/mock-data/status';
import { useState } from 'react';

interface StatusWithPercentProps {
   status: Status;
   percentComplete: number;
}

export function StatusWithPercent({ status, percentComplete }: StatusWithPercentProps) {
   const [open, setOpen] = useState(false);
   const [tempPercent, setTempPercent] = useState(percentComplete.toString());

   const handlePercentUpdate = () => {
      const newPercent = parseInt(tempPercent);
      if (newPercent >= 0 && newPercent <= 100) {
         // Mock update - in real app this would update the store
         console.log('Updating project progress:', newPercent);
         setOpen(false);
      }
   };

   return (
      <Popover open={open} onOpenChange={setOpen}>
         <PopoverTrigger asChild>
            <Button
               variant="ghost"
               size="xs"
               className="h-6 px-2 text-xs justify-start"
               style={{ color: status.color }}
            >
               {status.name} {percentComplete}%
            </Button>
         </PopoverTrigger>
         <PopoverContent className="w-48 p-3" align="start">
            <div className="space-y-2">
               <label className="text-xs font-medium">Progress</label>
               <div className="flex gap-2">
                  <Input
                     type="number"
                     min="0"
                     max="100"
                     value={tempPercent}
                     onChange={(e) => setTempPercent(e.target.value)}
                     className="h-8 text-xs"
                  />
                  <Button size="xs" onClick={handlePercentUpdate}>
                     Update
                  </Button>
               </div>
            </div>
         </PopoverContent>
      </Popover>
   );
}
```

**After (with useProjects hook):**
```typescript
'use client';

import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { useProjects, Project } from '@/hooks/useProjects';
import { useState } from 'react';
import { toast } from 'sonner';

interface StatusWithPercentProps {
   project: Project;
}

export function StatusWithPercent({ project }: StatusWithPercentProps) {
   const [open, setOpen] = useState(false);
   const [tempPercent, setTempPercent] = useState(project.percent_complete.toString());
   const { updateProjectProgress } = useProjects();

   const handlePercentUpdate = async () => {
      const newPercent = parseInt(tempPercent);
      if (newPercent >= 0 && newPercent <= 100) {
         try {
            await updateProjectProgress(project.id, newPercent);
            toast.success('Project progress updated successfully');
            setOpen(false);
         } catch (error) {
            toast.error('Failed to update project progress');
            console.error('Error updating project progress:', error);
         }
      } else {
         toast.error('Progress must be between 0 and 100');
      }
   };

   return (
      <Popover open={open} onOpenChange={setOpen}>
         <PopoverTrigger asChild>
            <Button
               variant="ghost"
               size="xs"
               className="h-6 px-2 text-xs justify-start"
               style={{ color: project.status?.color }}
            >
               {project.status?.name || 'No Status'} {project.percent_complete}%
            </Button>
         </PopoverTrigger>
         <PopoverContent className="w-48 p-3" align="start">
            <div className="space-y-2">
               <label className="text-xs font-medium">Progress</label>
               <div className="flex gap-2">
                  <Input
                     type="number"
                     min="0"
                     max="100"
                     value={tempPercent}
                     onChange={(e) => setTempPercent(e.target.value)}
                     className="h-8 text-xs"
                  />
                  <Button size="xs" onClick={handlePercentUpdate}>
                     Update
                  </Button>
               </div>
            </div>
         </PopoverContent>
      </Popover>
   );
}
```

## Key Migration Points

### 1. Async Operations
- **Before**: Synchronous store updates
- **After**: Async operations with proper error handling

### 2. Error Handling
- **Before**: No error handling
- **After**: Comprehensive try-catch blocks with user feedback

### 3. Loading States
- **Before**: No loading states
- **After**: Built-in loading states from the hook

### 4. Real-time Updates
- **Before**: Manual state management
- **After**: Automatic real-time synchronization

### 5. Data Structure
- **Before**: Mock data with simple interfaces
- **After**: Complete database relationships with joined data

### 6. Type Safety
- **Before**: Basic TypeScript interfaces
- **After**: Comprehensive type definitions with database relationships

## Migration Checklist

- [ ] Replace all `projects` mock data imports with `useProjects` hook
- [ ] Add loading and error state handling to all components
- [ ] Convert all project update operations to async functions
- [ ] Add proper error handling with user feedback (toast notifications)
- [ ] Update TypeScript interfaces to match the new Project interface
- [ ] Test all CRUD operations with the new hook
- [ ] Verify real-time updates are working correctly
- [ ] Update any project filtering or searching logic
- [ ] Test optimistic updates and error rollback scenarios
- [ ] Update any project-related forms to use the new create/update functions

## Testing Strategy

1. **Unit Tests**: Test individual hook functions
2. **Integration Tests**: Test component interactions with the hook
3. **E2E Tests**: Test complete user workflows
4. **Real-time Tests**: Verify multi-client synchronization
5. **Error Scenarios**: Test network failures and permission errors

This migration provides a robust, scalable foundation for project management with real-time capabilities and comprehensive error handling.
