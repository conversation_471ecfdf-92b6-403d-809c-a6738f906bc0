# Supabase Database Schema for Issues Management System

## Overview
This document outlines the complete database schema for the issues management system extracted from the Circle project, adapted for Supabase with PostgreSQL.

## Database Tables

### 1. Users Table (profiles)
```sql
-- Users/Profiles table (extends Supabase auth.users)
CREATE TABLE profiles (
  id UUID REFERENCES auth.users(id) ON DELETE CASCADE PRIMARY KEY,
  name TEXT NOT NULL,
  email TEXT NOT NULL,
  avatar_url TEXT,
  status TEXT CHECK (status IN ('online', 'offline', 'away')) DEFAULT 'offline',
  role TEXT CHECK (role IN ('Member', 'Admin', 'Guest')) DEFAULT 'Member',
  joined_date TIMESTAMPTZ DEFAULT NOW(),
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);
```

### 2. Teams Table
```sql
CREATE TABLE teams (
  id TEXT PRIMARY KEY,
  name TEXT NOT NULL,
  icon TEXT,
  color TEXT,
  description TEXT,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);
```

### 3. Team Members Junction Table
```sql
CREATE TABLE team_members (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  team_id TEXT REFERENCES teams(id) ON DELETE CASCADE,
  user_id UUID REFERENCES profiles(id) ON DELETE CASCADE,
  joined BOOLEAN DEFAULT false,
  joined_at TIMESTAMPTZ DEFAULT NOW(),
  UNIQUE(team_id, user_id)
);
```

### 4. Projects Table
```sql
CREATE TABLE projects (
  id TEXT PRIMARY KEY,
  name TEXT NOT NULL,
  description TEXT,
  icon TEXT,
  percent_complete INTEGER DEFAULT 0 CHECK (percent_complete >= 0 AND percent_complete <= 100),
  start_date DATE,
  target_date DATE,
  lead_id UUID REFERENCES profiles(id),
  status_id TEXT,
  priority_id TEXT,
  health_id TEXT CHECK (health_id IN ('no-update', 'off-track', 'on-track', 'at-risk')),
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);
```

### 5. Cycles Table
```sql
CREATE TABLE cycles (
  id TEXT PRIMARY KEY,
  number INTEGER NOT NULL,
  name TEXT NOT NULL,
  team_id TEXT REFERENCES teams(id) ON DELETE CASCADE,
  start_date DATE NOT NULL,
  end_date DATE NOT NULL,
  progress INTEGER DEFAULT 0 CHECK (progress >= 0 AND progress <= 100),
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);
```

### 6. Status Table
```sql
CREATE TABLE status (
  id TEXT PRIMARY KEY,
  name TEXT NOT NULL,
  color TEXT NOT NULL,
  icon_name TEXT,
  sort_order INTEGER DEFAULT 0,
  created_at TIMESTAMPTZ DEFAULT NOW()
);
```

### 7. Priorities Table
```sql
CREATE TABLE priorities (
  id TEXT PRIMARY KEY,
  name TEXT NOT NULL,
  icon_name TEXT,
  sort_order INTEGER DEFAULT 0,
  created_at TIMESTAMPTZ DEFAULT NOW()
);
```

### 8. Labels Table
```sql
CREATE TABLE labels (
  id TEXT PRIMARY KEY,
  name TEXT NOT NULL,
  color TEXT NOT NULL,
  created_at TIMESTAMPTZ DEFAULT NOW()
);
```

### 9. Issues Table (Main Entity)
```sql
CREATE TABLE issues (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  identifier TEXT NOT NULL UNIQUE,
  title TEXT NOT NULL,
  description TEXT DEFAULT '',
  status_id TEXT REFERENCES status(id) NOT NULL,
  assignee_id UUID REFERENCES profiles(id),
  priority_id TEXT REFERENCES priorities(id) NOT NULL,
  project_id TEXT REFERENCES projects(id),
  cycle_id TEXT REFERENCES cycles(id),
  parent_issue_id UUID REFERENCES issues(id),
  rank TEXT NOT NULL, -- LexoRank for ordering
  due_date DATE,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  created_by UUID REFERENCES profiles(id) NOT NULL
);
```

### 10. Issue Labels Junction Table
```sql
CREATE TABLE issue_labels (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  issue_id UUID REFERENCES issues(id) ON DELETE CASCADE,
  label_id TEXT REFERENCES labels(id) ON DELETE CASCADE,
  UNIQUE(issue_id, label_id)
);
```

### 11. Project Teams Junction Table
```sql
CREATE TABLE project_teams (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  project_id TEXT REFERENCES projects(id) ON DELETE CASCADE,
  team_id TEXT REFERENCES teams(id) ON DELETE CASCADE,
  UNIQUE(project_id, team_id)
);
```

## Foreign Key Constraints

```sql
-- Add foreign key constraints for projects
ALTER TABLE projects 
ADD CONSTRAINT fk_projects_status 
FOREIGN KEY (status_id) REFERENCES status(id);

ALTER TABLE projects 
ADD CONSTRAINT fk_projects_priority 
FOREIGN KEY (priority_id) REFERENCES priorities(id);
```

## Indexes for Performance

```sql
-- Issues table indexes
CREATE INDEX idx_issues_status_id ON issues(status_id);
CREATE INDEX idx_issues_assignee_id ON issues(assignee_id);
CREATE INDEX idx_issues_priority_id ON issues(priority_id);
CREATE INDEX idx_issues_project_id ON issues(project_id);
CREATE INDEX idx_issues_cycle_id ON issues(cycle_id);
CREATE INDEX idx_issues_parent_issue_id ON issues(parent_issue_id);
CREATE INDEX idx_issues_created_at ON issues(created_at);
CREATE INDEX idx_issues_rank ON issues(rank);
CREATE INDEX idx_issues_identifier ON issues(identifier);

-- Text search index for issues
CREATE INDEX idx_issues_search ON issues USING gin(to_tsvector('english', title || ' ' || description));

-- Team members indexes
CREATE INDEX idx_team_members_team_id ON team_members(team_id);
CREATE INDEX idx_team_members_user_id ON team_members(user_id);

-- Issue labels indexes
CREATE INDEX idx_issue_labels_issue_id ON issue_labels(issue_id);
CREATE INDEX idx_issue_labels_label_id ON issue_labels(label_id);

-- Project teams indexes
CREATE INDEX idx_project_teams_project_id ON project_teams(project_id);
CREATE INDEX idx_project_teams_team_id ON project_teams(team_id);
```

## Row Level Security (RLS) Policies

### Profiles Table
```sql
ALTER TABLE profiles ENABLE ROW LEVEL SECURITY;

-- Users can read all profiles
CREATE POLICY "Users can view all profiles" ON profiles
  FOR SELECT USING (true);

-- Users can update their own profile
CREATE POLICY "Users can update own profile" ON profiles
  FOR UPDATE USING (auth.uid() = id);
```

### Teams Table
```sql
ALTER TABLE teams ENABLE ROW LEVEL SECURITY;

-- Users can read all teams
CREATE POLICY "Users can view all teams" ON teams
  FOR SELECT USING (true);

-- Only admins can create/update/delete teams
CREATE POLICY "Admins can manage teams" ON teams
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM profiles 
      WHERE id = auth.uid() AND role = 'Admin'
    )
  );
```

### Team Members Table
```sql
ALTER TABLE team_members ENABLE ROW LEVEL SECURITY;

-- Users can view all team memberships
CREATE POLICY "Users can view team memberships" ON team_members
  FOR SELECT USING (true);

-- Users can manage their own team memberships
CREATE POLICY "Users can manage own memberships" ON team_members
  FOR ALL USING (auth.uid() = user_id);

-- Admins can manage all memberships
CREATE POLICY "Admins can manage all memberships" ON team_members
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM profiles 
      WHERE id = auth.uid() AND role = 'Admin'
    )
  );
```

### Projects Table
```sql
ALTER TABLE projects ENABLE ROW LEVEL SECURITY;

-- Users can read all projects
CREATE POLICY "Users can view all projects" ON projects
  FOR SELECT USING (true);

-- Project leads and admins can update projects
CREATE POLICY "Project leads and admins can update projects" ON projects
  FOR UPDATE USING (
    auth.uid() = lead_id OR 
    EXISTS (
      SELECT 1 FROM profiles 
      WHERE id = auth.uid() AND role = 'Admin'
    )
  );

-- Admins can create/delete projects
CREATE POLICY "Admins can manage projects" ON projects
  FOR INSERT WITH CHECK (
    EXISTS (
      SELECT 1 FROM profiles 
      WHERE id = auth.uid() AND role = 'Admin'
    )
  );

CREATE POLICY "Admins can delete projects" ON projects
  FOR DELETE USING (
    EXISTS (
      SELECT 1 FROM profiles 
      WHERE id = auth.uid() AND role = 'Admin'
    )
  );
```

### Issues Table
```sql
ALTER TABLE issues ENABLE ROW LEVEL SECURITY;

-- Users can read all issues
CREATE POLICY "Users can view all issues" ON issues
  FOR SELECT USING (true);

-- Users can create issues
CREATE POLICY "Users can create issues" ON issues
  FOR INSERT WITH CHECK (auth.uid() = created_by);

-- Users can update issues they created or are assigned to
CREATE POLICY "Users can update own or assigned issues" ON issues
  FOR UPDATE USING (
    auth.uid() = created_by OR 
    auth.uid() = assignee_id OR
    EXISTS (
      SELECT 1 FROM profiles 
      WHERE id = auth.uid() AND role = 'Admin'
    )
  );

-- Users can delete issues they created, admins can delete any
CREATE POLICY "Users can delete own issues, admins can delete any" ON issues
  FOR DELETE USING (
    auth.uid() = created_by OR
    EXISTS (
      SELECT 1 FROM profiles 
      WHERE id = auth.uid() AND role = 'Admin'
    )
  );
```

### Reference Tables (Status, Priorities, Labels)
```sql
-- Status table
ALTER TABLE status ENABLE ROW LEVEL SECURITY;
CREATE POLICY "Users can view status" ON status FOR SELECT USING (true);
CREATE POLICY "Admins can manage status" ON status FOR ALL USING (
  EXISTS (SELECT 1 FROM profiles WHERE id = auth.uid() AND role = 'Admin')
);

-- Priorities table
ALTER TABLE priorities ENABLE ROW LEVEL SECURITY;
CREATE POLICY "Users can view priorities" ON priorities FOR SELECT USING (true);
CREATE POLICY "Admins can manage priorities" ON priorities FOR ALL USING (
  EXISTS (SELECT 1 FROM profiles WHERE id = auth.uid() AND role = 'Admin')
);

-- Labels table
ALTER TABLE labels ENABLE ROW LEVEL SECURITY;
CREATE POLICY "Users can view labels" ON labels FOR SELECT USING (true);
CREATE POLICY "Admins can manage labels" ON labels FOR ALL USING (
  EXISTS (SELECT 1 FROM profiles WHERE id = auth.uid() AND role = 'Admin')
);
```

### Junction Tables
```sql
-- Issue Labels
ALTER TABLE issue_labels ENABLE ROW LEVEL SECURITY;
CREATE POLICY "Users can view issue labels" ON issue_labels FOR SELECT USING (true);
CREATE POLICY "Users can manage issue labels" ON issue_labels FOR ALL USING (
  EXISTS (
    SELECT 1 FROM issues 
    WHERE id = issue_id AND (created_by = auth.uid() OR assignee_id = auth.uid())
  ) OR
  EXISTS (SELECT 1 FROM profiles WHERE id = auth.uid() AND role = 'Admin')
);

-- Project Teams
ALTER TABLE project_teams ENABLE ROW LEVEL SECURITY;
CREATE POLICY "Users can view project teams" ON project_teams FOR SELECT USING (true);
CREATE POLICY "Admins can manage project teams" ON project_teams FOR ALL USING (
  EXISTS (SELECT 1 FROM profiles WHERE id = auth.uid() AND role = 'Admin')
);
```

## Database Functions

### Update Timestamp Function
```sql
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ language 'plpgsql';
```

### Apply Update Triggers
```sql
-- Apply to all tables with updated_at column
CREATE TRIGGER update_profiles_updated_at BEFORE UPDATE ON profiles
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_teams_updated_at BEFORE UPDATE ON teams
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_projects_updated_at BEFORE UPDATE ON projects
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_cycles_updated_at BEFORE UPDATE ON cycles
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_issues_updated_at BEFORE UPDATE ON issues
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
```

## Sample Data Insertion Scripts

### Insert Status Data
```sql
INSERT INTO status (id, name, color, icon_name, sort_order) VALUES
('in-progress', 'In Progress', '#facc15', 'InProgressIcon', 1),
('technical-review', 'Technical Review', '#22c55e', 'TechnicalReviewIcon', 2),
('completed', 'Completed', '#8b5cf6', 'CompletedIcon', 3),
('paused', 'Paused', '#0ea5e9', 'PausedIcon', 4),
('to-do', 'Todo', '#f97316', 'ToDoIcon', 5),
('backlog', 'Backlog', '#ec4899', 'BacklogIcon', 6);
```

### Insert Priorities Data
```sql
INSERT INTO priorities (id, name, icon_name, sort_order) VALUES
('no-priority', 'No priority', 'NoPriorityIcon', 1),
('urgent', 'Urgent', 'UrgentPriorityIcon', 2),
('high', 'High', 'HighPriorityIcon', 3),
('medium', 'Medium', 'MediumPriorityIcon', 4),
('low', 'Low', 'LowPriorityIcon', 5);
```

### Insert Labels Data
```sql
INSERT INTO labels (id, name, color) VALUES
('ui', 'UI Enhancement', 'purple'),
('bug', 'Bug', 'red'),
('feature', 'Feature', 'green'),
('documentation', 'Documentation', 'blue'),
('refactor', 'Refactor', 'yellow'),
('performance', 'Performance', 'orange'),
('design', 'Design', 'pink'),
('security', 'Security', 'gray'),
('accessibility', 'Accessibility', 'indigo'),
('testing', 'Testing', 'teal'),
('internationalization', 'Internationalization', 'cyan');
```

This schema provides a solid foundation for the issues management system with proper relationships, security policies, and performance optimizations.
