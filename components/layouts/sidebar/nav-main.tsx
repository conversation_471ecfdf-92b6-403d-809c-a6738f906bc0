'use client';

import { ChevronRight, type LucideIcon } from 'lucide-react';
import Link from 'next/link';
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from '@/components/ui/collapsible';
import {
  SidebarGroup,
  SidebarGroupLabel,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarMenuSub,
  SidebarMenuSubButton,
  SidebarMenuSubItem,
} from '@/components/ui/sidebar';

export function NavMain({
  items,
}: {
  items: {
    title: string;
    items?: {
      icon?: LucideIcon;
      isActive?: boolean;
      title: string;
      url: string;
      items?: {
        isActive?: boolean;
        icon?: LucideIcon;
        title: string;
        url: string;
      }[];
    }[];
  }[];
}) {
  return (
    <>
      {items.map((item) => (
        <SidebarGroup key={item.title}>
          <SidebarGroupLabel>{item.title}</SidebarGroupLabel>
          <SidebarMenu>
            {item.items?.map((subItem) => (
              <Collapsible
                key={subItem.title}
                asChild
                defaultOpen={subItem.isActive}
                className='group/collapsible'
              >
                <SidebarMenuItem>
                  {!subItem.items ? (
                    <SidebarMenuButton asChild tooltip={subItem.title}>
                      <Link href={subItem.url}>
                        {subItem.icon && <subItem.icon />}
                        <span>{subItem.title}</span>
                      </Link>
                    </SidebarMenuButton>
                  ) : (
                    <CollapsibleTrigger asChild>
                      <SidebarMenuButton tooltip={subItem.title}>
                        {subItem.icon && <subItem.icon />}
                        <span>{subItem.title}</span>
                        {!subItem.items ? null : (
                          <ChevronRight className='ml-auto transition-transform duration-200 group-data-[state=open]/collapsible:rotate-90' />
                        )}
                      </SidebarMenuButton>
                    </CollapsibleTrigger>
                  )}

                  <CollapsibleContent>
                    <SidebarMenuSub>
                      {subItem.items?.map((esubItem) => (
                        <SidebarMenuSubItem key={esubItem.title}>
                          <SidebarMenuSubButton asChild>
                            <Link href={esubItem.url}>
                              {esubItem.icon && <esubItem.icon />}
                              <span>{esubItem.title}</span>
                            </Link>
                          </SidebarMenuSubButton>
                        </SidebarMenuSubItem>
                      ))}
                    </SidebarMenuSub>
                  </CollapsibleContent>
                </SidebarMenuItem>
              </Collapsible>
            ))}
          </SidebarMenu>
        </SidebarGroup>
      ))}
    </>
  );
}
