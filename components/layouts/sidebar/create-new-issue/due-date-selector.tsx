'use client';

import { Button } from '@/components/ui/button';
import { Calendar } from '@/components/ui/calendar';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { CalendarIcon, X } from 'lucide-react';
import { useState } from 'react';
import { format } from 'date-fns';
import { cn } from '@/lib/utils';

interface DueDateSelectorProps {
  value: string;
  onChange: (date: string) => void;
}

export function DueDateSelector({ value, onChange }: DueDateSelectorProps) {
  const [open, setOpen] = useState<boolean>(false);

  // Convert string value to Date object
  const selectedDate = value ? new Date(value) : undefined;

  const handleDateSelect = (date: Date | undefined) => {
    if (date) {
      // Convert to ISO string for form storage
      onChange(date.toISOString());
    } else {
      onChange('');
    }
    setOpen(false);
  };

  const handleClearDate = (e: React.MouseEvent) => {
    e.stopPropagation();
    onChange('');
  };

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button
          className={cn(
            'flex items-center justify-center gap-1',
            !selectedDate && 'size-7'
          )}
          size={selectedDate ? 'sm' : 'icon'}
          variant='secondary'
          aria-expanded={open}
        >
          <CalendarIcon className='size-4' />
          {selectedDate && (
            <>
              <span className='text-xs'>{format(selectedDate, 'MMM dd')}</span>
              <X
                className='size-3 ml-1 hover:bg-gray-200 rounded'
                onClick={handleClearDate}
              />
            </>
          )}
        </Button>
      </PopoverTrigger>
      <PopoverContent className='border-input w-auto p-0' align='start'>
        <Calendar
          mode='single'
          selected={selectedDate}
          onSelect={handleDateSelect}
          disabled={(date) => date < new Date(new Date().setHours(0, 0, 0, 0))}
        />
      </PopoverContent>
    </Popover>
  );
}
