import { zod<PERSON>esolver } from '@hookform/resolvers/zod';
import { Heart, PenBox } from 'lucide-react';
import { useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import { toast } from 'sonner';
import { z } from 'zod';
import { But<PERSON> } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Textarea } from '@/components/ui/textarea';
import { useIssues } from '@/hooks/use-db';
import { useCreateIssueStore } from '@/lib/store/create-issue-store';
import type { CreateIssueInput } from '@/lib/supabase/database-modules';
import { IssueRankUtils } from '@/lib/utils/lexorank-utils';
import { extractErrorMessage } from '@/lib/utils/error-utils';

import { AssigneeSelector } from './assignee-selector';
import { DueDateSelector } from './due-date-selector';
import { LabelSelector } from './label-selector';
import { PrioritySelector } from './priority-selector';
import { ProjectSelector } from './project-selector';
import { StatusSelector } from './status-selector';

// Zod schema for form validation
const createIssueSchema = z.object({
  title: z.string().min(1, 'Title is required').max(255, 'Title is too long'),
  description: z.string().optional(),
  status_id: z.string().min(1, 'Status is required'),
  priority_id: z.string().min(1, 'Priority is required'),
  assignee_id: z.string().optional(),
  project_id: z.string().optional(),
  labels: z.array(z.string()).optional().default([]),
  due_date: z.string().optional(),
});

type CreateIssueFormData = z.infer<typeof createIssueSchema>;

interface CreateNewIssueProps {
  showTrigger?: boolean;
}

export function CreateNewIssue({ showTrigger = true }: CreateNewIssueProps) {
  const [createMore, setCreateMore] = useState<boolean>(false);
  const { isOpen, defaultStatus, openModal, closeModal } =
    useCreateIssueStore();
  const { addIssue, issues } = useIssues();

  const form = useForm({
    resolver: zodResolver(createIssueSchema),
    defaultValues: {
      title: '',
      description: '',
      status_id: '',
      priority_id: '',
      assignee_id: '',
      project_id: '',
      labels: [] as string[],
      due_date: '',
    },
  });

  // Reset form when modal opens/closes
  useEffect(() => {
    if (isOpen) {
      form.reset({
        title: '',
        description: '',
        status_id: defaultStatus || '',
        priority_id: '',
        assignee_id: '',
        project_id: '',
        labels: [],
        due_date: '',
      });
    }
  }, [isOpen, defaultStatus, form]);

  const onSubmit = (data: CreateIssueFormData): Promise<void> => {
    return new Promise((resolve, reject) => {
      // Generate rank for new issue (add to top of status column)
      const existingIssuesInStatus = issues.filter(
        (issue) => issue.status_id === data.status_id
      );
      const rank = IssueRankUtils.getTopRank(existingIssuesInStatus);

      const issueInput: CreateIssueInput = {
        title: data.title.trim(),
        description: data.description?.trim() || undefined,
        status_id: data.status_id,
        priority_id: data.priority_id,
        assignee_id: data.assignee_id || undefined,
        project_id: data.project_id || undefined,
        labels: data.labels || [],
        due_date: data.due_date || undefined,
        rank,
      };

      addIssue(issueInput)
        .then(() => {
          toast.success('Issue created successfully');

          if (!createMore) {
            closeModal();
          } else {
            // Reset form for creating more issues but keep some values
            form.reset({
              title: '',
              description: '',
              status_id: data.status_id, // Keep same status
              priority_id: data.priority_id, // Keep same priority
              assignee_id: '',
              project_id: data.project_id || '', // Keep same project
              labels: [],
              due_date: '',
            });
          }
          resolve();
        })
        .catch((error) => {
          console.error('Failed to create issue:', error);

          // Extract meaningful error message
          const errorMessage = extractErrorMessage(
            error,
            'Failed to create issue. Please try again.'
          );

          toast.error(errorMessage);
          reject(error);
        });
    });
  };

  return (
    <Dialog
      open={isOpen}
      onOpenChange={(value) => (value ? openModal() : closeModal())}
    >
      {showTrigger && (
        <DialogTrigger asChild>
          <Button className='size-8 shrink-0' variant={'outline'} size='icon'>
            <PenBox className='size-4' />
          </Button>
        </DialogTrigger>
      )}
      <DialogContent className='w-full sm:max-w-[750px] p-0 shadow-xl top-[30%]'>
        <DialogHeader>
          <DialogTitle>
            <div className='flex items-center px-4 pt-4 gap-2'>
              <Button size='sm' variant='outline' className='gap-1.5'>
                <Heart className='size-4 text-orange-500 fill-orange-500' />
                <span className='font-medium'>CORE</span>
              </Button>
            </div>
          </DialogTitle>
        </DialogHeader>

        <Form {...form}>
          <form
            id='create-issue-form'
            onSubmit={form.handleSubmit(onSubmit)}
            className='px-4 pb-0 space-y-3 w-full'
          >
            <FormField
              control={form.control}
              name='title'
              render={({ field }) => (
                <FormItem>
                  <FormControl>
                    <Input
                      {...field}
                      className='border-none w-full shadow-none outline-none text-2xl font-medium px-0 h-auto focus-visible:ring-0 overflow-hidden text-ellipsis whitespace-normal break-words'
                      placeholder='Issue title'
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name='description'
              render={({ field }) => (
                <FormItem>
                  <FormControl>
                    <Textarea
                      {...field}
                      className='border-none w-full shadow-none outline-none resize-none px-0 min-h-16 focus-visible:ring-0 break-words whitespace-normal overflow-wrap'
                      placeholder='Add description...'
                    />
                  </FormControl>
                </FormItem>
              )}
            />

            <div className='w-full flex items-center justify-start gap-1.5 flex-wrap'>
              <StatusSelector
                value={form.watch('status_id')}
                onChange={(value) => form.setValue('status_id', value)}
              />
              <PrioritySelector
                value={form.watch('priority_id')}
                onChange={(value) => form.setValue('priority_id', value)}
              />
              <AssigneeSelector
                value={form.watch('assignee_id') || ''}
                onChange={(value) => form.setValue('assignee_id', value)}
              />
              <ProjectSelector
                value={form.watch('project_id') || ''}
                onChange={(value) => form.setValue('project_id', value)}
              />
              <LabelSelector
                value={form.watch('labels') || []}
                onChange={(value) => form.setValue('labels', value)}
              />
              <DueDateSelector
                value={form.watch('due_date') || ''}
                onChange={(value) => form.setValue('due_date', value)}
              />
            </div>
          </form>
        </Form>
        <div className='flex items-center justify-between py-2.5 px-4 w-full border-t'>
          <div className='flex items-center gap-2'>
            <div className='flex items-center space-x-2'>
              <Switch
                id='create-more'
                checked={createMore}
                onCheckedChange={setCreateMore}
              />
              <Label htmlFor='create-more'>Create more</Label>
            </div>
          </div>
          <Button
            size='sm'
            type='submit'
            form='create-issue-form'
            disabled={form.formState.isSubmitting}
          >
            {form.formState.isSubmitting ? 'Creating...' : 'Create issue'}
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
}
