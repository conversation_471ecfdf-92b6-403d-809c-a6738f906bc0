'use client';

import { Button } from '@/components/ui/button';
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from '@/components/ui/command';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { CheckIcon, TagIcon } from 'lucide-react';
import { useId, useState } from 'react';
import { cn } from '@/lib/utils';
import { useLabels } from '@/hooks/use-db';
import { getHexColor } from '@/lib/utils/color-utils';

interface LabelSelectorProps {
  value: string[];
  onChange: (labelIds: string[]) => void;
}

export function LabelSelector({ value, onChange }: LabelSelectorProps) {
  const id = useId();
  const [open, setOpen] = useState<boolean>(false);
  const { labels: labelOptions, loading } = useLabels();

  const handleLabelToggle = (labelId: string) => {
    const isSelected = value.includes(labelId);
    let newLabels: string[];

    if (isSelected) {
      newLabels = value.filter((id) => id !== labelId);
    } else {
      newLabels = [...value, labelId];
    }

    onChange(newLabels);
  };

  return (
    <div className='*:not-first:mt-2'>
      <Popover open={open} onOpenChange={setOpen}>
        <PopoverTrigger asChild>
          <Button
            id={id}
            className={cn(
              'flex items-center justify-center gap-1',
              value.length === 0 && 'size-7'
            )}
            size={value.length > 0 ? 'sm' : 'icon'}
            variant='secondary'
            // role='combobox'
            aria-expanded={open}
            disabled={loading}
          >
            <TagIcon className='size-4' />
            {value.length > 0 && (
              <div className='flex -space-x-0.5'>
                {value.slice(0, 3).map((labelId) => {
                  const label = labelOptions.find((l) => l.id === labelId);
                  return label ? (
                    <div
                      key={label.id}
                      className='size-3 rounded-full'
                      style={{ backgroundColor: getHexColor(label.color) }}
                    />
                  ) : null;
                })}
                {value.length > 3 && (
                  <div className='size-3 rounded-full bg-gray-400 flex items-center justify-center'>
                    <span className='text-xs text-white'>
                      +{value.length - 3}
                    </span>
                  </div>
                )}
              </div>
            )}
          </Button>
        </PopoverTrigger>
        <PopoverContent
          className='border-input w-full min-w-[var(--radix-popper-anchor-width)] p-0'
          align='start'
        >
          <Command>
            <CommandInput placeholder='Search labels...' />
            <CommandList>
              <CommandEmpty>No labels found.</CommandEmpty>
              <CommandGroup>
                {loading ? (
                  <CommandItem disabled>
                    <span>Loading labels...</span>
                  </CommandItem>
                ) : (
                  labelOptions.map((label) => {
                    const isSelected = value.includes(label.id);
                    return (
                      <CommandItem
                        key={label.id}
                        value={label.id}
                        onSelect={() => handleLabelToggle(label.id)}
                        className='flex items-center justify-between'
                      >
                        <div className='flex items-center gap-2'>
                          <div
                            className='size-3 rounded-full'
                            style={{
                              backgroundColor: getHexColor(label.color),
                            }}
                          />
                          <span>{label.name}</span>
                        </div>
                        {isSelected && (
                          <CheckIcon size={16} className='ml-auto' />
                        )}
                      </CommandItem>
                    );
                  })
                )}
              </CommandGroup>
            </CommandList>
          </Command>
        </PopoverContent>
      </Popover>
    </div>
  );
}
