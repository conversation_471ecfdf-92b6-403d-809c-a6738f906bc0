import { Slot } from '@radix-ui/react-slot';
import { cva, type VariantProps } from 'class-variance-authority';
import type * as React from 'react';

import { cn } from '@/lib/utils/cn';

const buttonVariants = cva(
  'inline-flex cursor-pointer items-center justify-center whitespace-nowrap font-medium text-sm ring-offset-white transition-colors duration-300 ease-linear focus-visible:outline-hidden focus-visible:ring-2 focus-visible:ring-neutral-950 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 dark:ring-offset-black-alt dark:focus-visible:ring-branddark-300',
  {
    variants: {
      variant: {
        main: 'w-full bg-accent-100 px-10 py-6 font-bold text-white text-xl capitalize transition-all duration-300 ease-linear hover:bg-accent-200 hover:text-white dark:bg-branddark-500 dark:hover:bg-branddark-200 dark:hover:text-white',
        main_sm:
          'bg-accent-100 px-6 py-3 font-bold text-white text-xl capitalize transition-all duration-300 ease-linear hover:bg-black-alt hover:text-white dark:bg-branddark-500 dark:hover:bg-black-alt dark:hover:text-white',
        main_no_style:
          'bg-accent-100 p-6 font-bold text-white text-xl capitalize transition-all duration-300 ease-linear hover:bg-black-alt hover:text-white dark:bg-branddark-500 dark:hover:bg-black-alt dark:hover:text-white',
        mainsm:
          'bg-accent-100 px-8 py-2 font-bold text-base text-white capitalize transition-all duration-300 ease-linear hover:bg-black-alt hover:text-white dark:bg-branddark-500 dark:hover:bg-black-alt dark:hover:text-white',
        default:
          'bg-black-alt/80 text-white hover:bg-black-alt dark:bg-branddark-300 dark:hover:bg-branddark-200',
        shadow:
          'text-neutral-500 shadow-light transition-all duration-300 ease-linear hover:text-black-alt dark:text-muted-foreground dark:shadow-dark dark:hover:text-foreground',
        shadow_dark:
          'text-white shadow-dark transition-all duration-300 ease-linear dark:text-foreground dark:shadow-dark dark:hover:bg-branddark-300 dark:hover:text-foreground',
        destructive:
          'bg-destructive text-neutral-50 hover:bg-destructive/90 dark:bg-destructive dark:hover:bg-destructive/90',
        outline:
          'border border-neutral-300 bg-white hover:bg-muted hover:text-accent-foreground dark:border-border dark:bg-background dark:hover:bg-muted dark:hover:text-accent-foreground',
        outlineTransparent:
          'border border-neutral-200 bg-transparent shadow-2xs hover:bg-muted/40 hover:text-accent-foreground dark:border-border dark:hover:bg-muted/40 dark:hover:text-accent-foreground',
        secondary:
          'bg-secondary text-neutral-900 hover:bg-secondary/80 dark:bg-secondary dark:text-secondary-foreground dark:hover:bg-secondary/80',
        ghost:
          'hover:text-accent-foreground dark:text-foreground dark:hover:text-accent-foreground',
        link: 'text-neutral-900 underline-offset-4 hover:underline dark:text-foreground dark:hover:underline',
      },
      size: {
        default: 'h-10 px-4 py-2',
        sm: 'h-8 px-4 py-2',
        lg: 'h-11 rounded-md px-8',
        icon: 'h-10 w-10',
        ghost: 'h-10 px-2',
        none: '',
      },
      icon: {
        none: '',
        left: 'space-x-2 py-2 pr-4 pl-2',
        right: 'space-x-2 py-2 pr-2 pl-4',
        both: 'space-x-2 px-4 py-2',
      },
    },
    defaultVariants: {
      variant: 'default',
      size: 'default',
      icon: 'none',
    },
  }
);

function Button({
  className,
  variant,
  size,
  asChild = false,
  ...props
}: React.ComponentProps<'button'> &
  VariantProps<typeof buttonVariants> & {
    asChild?: boolean;
  }) {
  const Comp = asChild ? Slot : 'button';

  return (
    <Comp
      data-slot='button'
      className={cn(buttonVariants({ variant, size, className }))}
      {...props}
    />
  );
}

export { Button, buttonVariants };
