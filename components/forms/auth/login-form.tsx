'use client';
import { zod<PERSON>esolver } from '@hookform/resolvers/zod';
import { useRouter } from 'next/navigation';
import { useForm } from 'react-hook-form';
import { toast } from 'sonner';
import * as z from 'zod';
import { Button } from '@/components/ui/button';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { userStore } from '@/lib/store/user';
import { supabaseClient } from '@/lib/supabase/auth/client';

const FormSchema = z.object({
  email: z.email({ message: 'Please enter a valid email address.' }),
  password: z.string().min(6),
});

export default function LoginForm() {
  const router = useRouter();
  const { updateUser } = userStore((state) => state);
  const form = useForm<z.infer<typeof FormSchema>>({
    resolver: zodResolver(FormSchema),
    defaultValues: {
      email: '',
      password: '',
    },
  });

  const onSubmit = (d: z.infer<typeof FormSchema>) => {
    return new Promise((resolve, reject) => {
      const supabase = supabaseClient;

      supabase.auth
        .signInWithPassword({
          email: d.email,
          password: d.password,
        })
        .then(({ data, error }) => {
          if (error) {
            console.log(error);
            reject(error);
          }
          updateUser(data.user);
          resolve(data);
          router.refresh();
        });
    });
  };

  return (
    <Form {...form}>
      <form
        onSubmit={form.handleSubmit((data) => {
          return toast.promise(onSubmit(data), {
            loading: 'Logging in...',
            success: (data: any) => `Logged in as ${data.user?.email}`,
            error: (err) => `Error: ${err.message}`,
          });
        })}
        className='w-full md:w-80 space-y-4 my-2'
      >
        <FormField
          control={form.control}
          name='email'
          render={({ field }) => (
            <FormItem>
              <FormControl>
                <Input
                  className='w-full'
                  type='email'
                  placeholder='Email'
                  {...field}
                />
              </FormControl>
              <FormMessage className='pl-2' />
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name='password'
          render={({ field }) => (
            <FormItem>
              <FormControl>
                <div className='flex h-10 w-full items-center space-x-2 relative'>
                  <Input
                    type={'password'}
                    className='w-full'
                    placeholder='Password'
                    {...field}
                  />
                </div>
              </FormControl>
              <FormMessage className='pl-2' />
            </FormItem>
          )}
        />
        <Button variant={'main'} className='w-full h-10' type='submit'>
          Login
        </Button>
      </form>
    </Form>
  );
}
