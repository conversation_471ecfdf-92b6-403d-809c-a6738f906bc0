'use client';

import { useEffect, useId, useState } from 'react';
import { CheckIcon } from 'lucide-react';

import { Button } from '@/components/ui/button';
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from '@/components/ui/command';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';

interface PriorityOption {
  id: string;
  name: string;
  icon_name: string;
  sort_order: number;
}

interface PrioritySelectorProps {
  priority: PriorityOption | null;
  onPriorityChange?: (priorityId: string) => void;
}

export function PrioritySelector({
  priority,
  onPriorityChange,
}: PrioritySelectorProps) {
  const id = useId();
  const [open, setOpen] = useState<boolean>(false);
  const [value, setValue] = useState<string>(priority?.id || '');
  const [priorityOptions, setPriorityOptions] = useState<PriorityOption[]>([]);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    setValue(priority?.id || '');
  }, [priority?.id]);

  // Load priority options from API
  useEffect(() => {
    const loadPriorityOptions = async () => {
      try {
        setLoading(true);
        const response = await fetch('/api/priorities');
        if (response.ok) {
          const { priorities } = await response.json();
          setPriorityOptions(priorities || []);
        }
      } catch (error) {
        console.error('Failed to load priority options:', error);
      } finally {
        setLoading(false);
      }
    };

    if (open) {
      loadPriorityOptions();
    }
  }, [open]);

  const handlePriorityChange = (priorityId: string) => {
    setValue(priorityId);
    setOpen(false);

    if (onPriorityChange) {
      onPriorityChange(priorityId);
    }
  };

  return (
    <div>
      <Popover open={open} onOpenChange={setOpen}>
        <PopoverTrigger asChild>
          <Button
            id={id}
            className='flex items-center justify-center'
            size='icon'
            variant='ghost'
            // role='combobox'
            aria-expanded={open}
          >
            {(() => {
              const selectedItem = priorityOptions.find(
                (item) => item.id === value
              );
              if (selectedItem) {
                return (
                  <span className='text-muted-foreground text-sm'>
                    {selectedItem.icon_name}
                  </span>
                );
              }
              return (
                <span className='text-muted-foreground text-sm'>
                  {loading ? '...' : '—'}
                </span>
              );
            })()}
          </Button>
        </PopoverTrigger>
        <PopoverContent className='border-input w-48 p-0' align='start'>
          <Command>
            <CommandInput placeholder='Set priority...' />
            <CommandList>
              <CommandEmpty>No priority found.</CommandEmpty>
              <CommandGroup>
                {loading ? (
                  <CommandItem disabled>
                    <span>Loading priorities...</span>
                  </CommandItem>
                ) : (
                  priorityOptions.map((item) => (
                    <CommandItem
                      key={item.id}
                      value={item.id}
                      onSelect={handlePriorityChange}
                      className='flex items-center justify-between'
                    >
                      <div className='flex items-center gap-2'>
                        <span className='text-muted-foreground text-sm'>
                          {item.icon_name}
                        </span>
                        <span className='text-xs'>{item.name}</span>
                      </div>
                      {value === item.id && (
                        <CheckIcon size={14} className='ml-auto' />
                      )}
                    </CommandItem>
                  ))
                )}
              </CommandGroup>
            </CommandList>
          </Command>
        </PopoverContent>
      </Popover>
    </div>
  );
}
