import { Folder } from 'lucide-react';

import { DatePicker } from './date-picker';
import { HealthPopover } from './health-popover';
import { LeadSelector } from './lead-selector';
import { PrioritySelector } from './priority-selector';
import { StatusWithPercent } from './status-with-percent';

interface ProjectData {
  id: string;
  name: string;
  description?: string | null;
  status?: string | null;
  priority?: {
    id: string;
    name: string;
    icon_name: string | null;
    sort_order: number | null;
  } | null;
  lead?: {
    id: string;
    full_name: string | null;
    email: string;
    avatar_url?: string | null;
  } | null;
  health?: {
    id: string;
    name: string;
    description?: string;
  } | null;
  target_date?: string | null;
  created_at: string;
  updated_at: string;
}

interface ProjectLineProps {
  project: ProjectData;
}

// Helper functions to convert database types to component types
const convertPriority = (priority: ProjectData['priority']) => {
  if (!priority) return null;
  return {
    id: priority.id,
    name: priority.name,
    icon_name: priority.icon_name || '',
    sort_order: priority.sort_order || 0,
  };
};

const convertLead = (lead: ProjectData['lead']) => {
  if (!lead) return null;
  return {
    id: lead.id,
    name: lead.full_name || lead.email,
    email: lead.email,
    avatar_url: lead.avatar_url,
  };
};

const convertHealth = (project: ProjectData) => {
  return {
    id: project.id,
    name: project.name,
    description: project.description,
    health: project.health
      ? {
          id: project.health.id,
          name: project.health.name,
          description: project.health.description,
        }
      : undefined,
    lead: project.lead
      ? {
          id: project.lead.id,
          name: project.lead.full_name || project.lead.email,
          avatar_url: project.lead.avatar_url,
        }
      : undefined,
  };
};

const convertStatus = (status: string | null | undefined) => {
  if (!status) return null;
  return {
    id: status,
    name: status,
    color: '#6b7280', // Default gray color
    sort_order: 0,
  };
};

const getPercentComplete = (status: string | null | undefined) => {
  switch (status?.toLowerCase()) {
    case 'completed':
      return 100;
    case 'in progress':
      return 60;
    case 'planning':
      return 20;
    default:
      return 0;
  }
};

export default function ProjectLine({ project }: ProjectLineProps) {
  return (
    <div className='w-full flex items-center py-3 px-6 border-b hover:bg-sidebar/50 border-muted-foreground/5 text-sm'>
      <div className='w-[60%] sm:w-[70%] xl:w-[46%] flex items-center gap-2'>
        <div className='relative'>
          <div className='inline-flex size-6 bg-muted/50 items-center justify-center rounded shrink-0'>
            <Folder className='size-4' />
          </div>
        </div>
        <div className='flex flex-col items-start overflow-hidden'>
          <span className='font-medium truncate w-full'>{project.name}</span>
        </div>
      </div>

      <div className='w-[20%] sm:w-[10%] xl:w-[13%]'>
        <HealthPopover project={convertHealth(project)} />
      </div>

      <div className='hidden w-[10%] sm:block'>
        <PrioritySelector priority={convertPriority(project.priority)} />
      </div>
      <div className='hidden xl:block xl:w-[13%]'>
        <LeadSelector lead={convertLead(project.lead)} />
      </div>

      <div className='hidden xl:block xl:w-[13%]'>
        <DatePicker
          date={project.target_date ? new Date(project.target_date) : undefined}
        />
      </div>

      <div className='w-[20%] sm:w-[10%]'>
        <StatusWithPercent
          status={convertStatus(project.status)}
          percentComplete={getPercentComplete(project.status)}
        />
      </div>
    </div>
  );
}
