'use client';

import { useEffect, useState } from 'react';

import { supabaseClient } from '@/lib/supabase/auth/client';
import ProjectLine from './project-line';

interface ProjectData {
  id: string;
  name: string;
  description?: string | null;
  status?: string | null;
  priority?: {
    id: string;
    name: string;
    icon_name: string | null;
    sort_order: number | null;
  } | null;
  lead?: {
    id: string;
    full_name: string | null;
    email: string;
    avatar_url?: string | null;
  } | null;
  health?: {
    id: string;
    name: string;
    description?: string;
  } | null;
  target_date?: string | null;
  created_at: string;
  updated_at: string;
}

export default function Projects() {
  const [projects, setProjects] = useState<ProjectData[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const loadProjects = async () => {
      try {
        setLoading(true);
        setError(null);

        const { data, error } = await supabaseClient
          .from('projects')
          .select(
            `
            id,
            name,
            description,
            status,
            target_date,
            created_at,
            updated_at,
            priority:priorities(id, name, icon_name, sort_order),
            lead:profiles!projects_lead_id_fkey(id, full_name, email, avatar_url)
          `
          )
          .order('created_at', { ascending: false });

        if (error) {
          console.error('Error loading projects:', error);
          setError('Failed to load projects');
        } else {
          setProjects(data || []);
        }
      } catch (err) {
        console.error('Error loading projects:', err);
        setError('Failed to load projects');
      } finally {
        setLoading(false);
      }
    };

    loadProjects();
  }, []);

  if (loading) {
    return (
      <div className='w-full p-6'>
        <div className='text-center text-muted-foreground'>
          Loading projects...
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className='w-full p-6'>
        <div className='text-center text-red-500'>{error}</div>
      </div>
    );
  }

  return (
    <div className='w-full'>
      <div className='bg-container px-6 py-1.5 text-sm flex items-center text-muted-foreground border-b sticky top-0 z-10'>
        <div className='w-[60%] sm:w-[70%] xl:w-[46%]'>Title</div>
        <div className='w-[20%] sm:w-[10%] xl:w-[13%] pl-2.5'>Health</div>
        <div className='hidden w-[10%] sm:block pl-2'>Priority</div>
        <div className='hidden xl:block xl:w-[13%] pl-2'>Lead</div>
        <div className='hidden xl:block xl:w-[13%] pl-2.5'>Target date</div>
        <div className='w-[20%] sm:w-[10%] pl-2'>Status</div>
      </div>

      <div className='w-full'>
        {projects.length === 0 ? (
          <div className='p-6 text-center text-muted-foreground'>
            No projects found
          </div>
        ) : (
          projects.map((project) => (
            <ProjectLine key={project.id} project={project} />
          ))
        )}
      </div>
    </div>
  );
}
