'use client';

import { CheckIcon } from 'lucide-react';
import { useEffect, useId, useState } from 'react';

import { Button } from '@/components/ui/button';
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from '@/components/ui/command';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { useIssues } from '@/hooks/use-db';
import { getPriorityIcon } from '@/lib/constants/priorities';

interface PriorityOption {
  id: string;
  name: string;
  icon_name: string;
  sort_order: number;
}

interface PrioritySelectorProps {
  priority: PriorityOption | null;
  issueId?: string;
  onChange?: (priority: PriorityOption | null) => void;
}

export function PrioritySelector({
  priority,
  issueId,
  onChange,
}: PrioritySelectorProps) {
  const id = useId();
  const [open, setOpen] = useState<boolean>(false);
  const [value, setValue] = useState<string>(priority?.id || '');
  const [priorityOptions, setPriorityOptions] = useState<PriorityOption[]>([]);
  const [loading, setLoading] = useState(false);
  const { updateIssue } = useIssues();

  useEffect(() => {
    setValue(priority?.id || '');
  }, [priority?.id]);

  // Load priority options from API
  useEffect(() => {
    const loadPriorityOptions = async () => {
      try {
        setLoading(true);
        const response = await fetch('/api/priorities');
        if (response.ok) {
          const { priorities } = await response.json();
          setPriorityOptions(priorities || []);
        }
      } catch (error) {
        console.error('Failed to load priority options:', error);
      } finally {
        setLoading(false);
      }
    };

    if (open) {
      loadPriorityOptions();
    }
  }, [open]);

  const handlePriorityChange = (priorityId: string) => {
    setValue(priorityId);
    setOpen(false);

    const newPriority =
      priorityOptions.find((p) => p.id === priorityId) || null;

    if (onChange) {
      onChange(newPriority);
    }

    if (issueId && newPriority) {
      updateIssue(issueId, { priority_id: newPriority.id })
        .then(() => {
          console.log('Issue priority updated successfully');
        })
        .catch((error) => {
          console.error('Failed to update issue priority:', error);
        });
    }
  };

  return (
    <div className='*:not-first:mt-2'>
      <Popover open={open} onOpenChange={setOpen}>
        <PopoverTrigger asChild>
          <Button
            id={id}
            className='size-7 flex items-center justify-center'
            size='icon'
            variant='ghost'
            // role='combobox'
            aria-expanded={open}
          >
            {(() => {
              const selectedItem = priorityOptions.find(
                (item) => item.id === value
              );
              if (selectedItem) {
                return (
                  <span className='text-muted-foreground text-sm'>
                    {selectedItem.icon_name}
                  </span>
                );
              }
              return (
                <span className='text-muted-foreground text-sm'>
                  {loading ? '...' : '—'}
                </span>
              );
            })()}
          </Button>
        </PopoverTrigger>
        <PopoverContent
          className='border-input w-full min-w-[var(--radix-popper-anchor-width)] p-0'
          align='start'
        >
          <Command>
            <CommandInput placeholder='Set priority...' />
            <CommandList>
              <CommandEmpty>No priority found.</CommandEmpty>
              <CommandGroup>
                {loading ? (
                  <CommandItem disabled>
                    <span>Loading priorities...</span>
                  </CommandItem>
                ) : (
                  priorityOptions.map((item) => (
                    <CommandItem
                      key={item.id}
                      value={item.id}
                      onSelect={handlePriorityChange}
                      className='flex items-center justify-between'
                    >
                      <div className='flex items-center gap-2'>
                        <span className='text-muted-foreground text-sm'>
                          {(() => {
                            const IconComponent = getPriorityIcon(
                              item.name.toLowerCase()
                            );
                            return IconComponent ? (
                              <IconComponent className='size-4' />
                            ) : null;
                          })()}
                        </span>
                        {item.name}
                      </div>
                      {value === item.id && (
                        <CheckIcon size={16} className='ml-auto' />
                      )}
                    </CommandItem>
                  ))
                )}
              </CommandGroup>
            </CommandList>
          </Command>
        </PopoverContent>
      </Popover>
    </div>
  );
}
