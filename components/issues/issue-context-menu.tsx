import {
  <PERSON>arm<PERSON>lock,
  ArrowRightLeft,
  BarChart3,
  Bell,
  Calendar<PERSON>lock,
  CheckCircle2,
  CircleCheck,
  Clipboard,
  Clock,
  Copy as CopyIcon,
  FileText,
  Flag,
  Folder,
  Link as LinkIcon,
  MessageSquare,
  Pencil,
  PlusSquare,
  Repeat2,
  Star,
  Tag,
  Trash2,
  User,
} from 'lucide-react';
import { useEffect, useState } from 'react';
import { toast } from 'sonner';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import {
  ContextMenuContent,
  ContextMenuGroup,
  ContextMenuItem,
  ContextMenuSeparator,
  ContextMenuShortcut,
  ContextMenuSub,
  ContextMenuSubContent,
  ContextMenuSubTrigger,
} from '@/components/ui/context-menu';

import {
  useIssues,
  useLabels,
  usePriorities,
  useProjects,
  useStatus,
} from '@/hooks/use-db';
import { supabaseClient } from '@/lib/supabase/auth/client';
import { getHexColor } from '@/lib/utils/color-utils';

interface UserOption {
  id: string;
  name: string;
  email: string;
  avatar_url?: string | null;
}

interface IssueContextMenuProps {
  issueId?: string;
}

export function IssueContextMenu({ issueId }: IssueContextMenuProps) {
  const [isSubscribed, setIsSubscribed] = useState(false);
  const [isFavorite, setIsFavorite] = useState(false);

  const { updateIssue, getIssueById } = useIssues();
  const { status: statusOptions } = useStatus();
  const { priorities: priorityOptions } = usePriorities();
  const { labels: labelOptions } = useLabels();
  const { projects: projectOptions } = useProjects();
  const [userOptions, setUserOptions] = useState<UserOption[]>([]);

  // Load user options (only users need to be loaded separately)
  useEffect(() => {
    const loadUserOptions = async () => {
      try {
        const { data: profiles } = await supabaseClient
          .from('profiles')
          .select('id, full_name, email, avatar_url')
          .eq('role', 'Collaborator')
          .order('full_name');

        if (profiles) {
          const users = profiles.map((profile) => ({
            id: profile.id,
            name: profile.full_name || profile.email,
            email: profile.email,
            avatar_url: profile.avatar_url,
          }));
          setUserOptions(users);
        }
      } catch (error) {
        console.error('Failed to load user options:', error);
      }
    };

    loadUserOptions();
  }, []);

  const handleStatusChange = (statusId: string) => {
    if (!issueId) return;
    const newStatus = statusOptions.find((s) => s.id === statusId);
    if (newStatus) {
      updateIssue(issueId, { status_id: statusId })
        .then(() => {
          toast.success(`Status updated to ${newStatus.name}`);
        })
        .catch((error) => {
          toast.error('Failed to update status');
          console.error('Failed to update status:', error);
        });
    }
  };

  const handlePriorityChange = (priorityId: string) => {
    if (!issueId) return;
    const newPriority = priorityOptions.find((p) => p.id === priorityId);
    if (newPriority) {
      updateIssue(issueId, { priority_id: priorityId })
        .then(() => {
          toast.success(`Priority updated to ${newPriority.name}`);
        })
        .catch((error) => {
          toast.error('Failed to update priority');
          console.error('Failed to update priority:', error);
        });
    }
  };

  const handleAssigneeChange = (userId: string | null) => {
    if (!issueId) return;
    const newAssignee = userId
      ? userOptions.find((u) => u.id === userId) || null
      : null;

    updateIssue(issueId, { assignee_id: userId })
      .then(() => {
        toast.success(
          newAssignee ? `Assigned to ${newAssignee.name}` : 'Unassigned'
        );
      })
      .catch((error) => {
        toast.error('Failed to update assignee');
        console.error('Failed to update assignee:', error);
      });
  };

  const handleLabelToggle = (labelId: string) => {
    if (!issueId) return;
    const issue = getIssueById(issueId);
    const label = labelOptions.find((l) => l.id === labelId);

    if (!issue || !label) return;

    const hasLabel = issue.labels?.some((l) => l.id === labelId) || false;

    // For now, just update the issue with the new label array
    const currentLabels = issue.labels || [];
    let newLabels: typeof currentLabels;

    if (hasLabel) {
      newLabels = currentLabels.filter((l) => l.id !== labelId);
      toast.success(`Removed label: ${label.name}`);
    } else {
      newLabels = [...currentLabels, label];
      toast.success(`Added label: ${label.name}`);
    }

    updateIssue(issueId, { labels: newLabels }).catch((error) => {
      toast.error('Failed to update labels');
      console.error('Failed to update labels:', error);
    });
  };

  const handleProjectChange = (projectId: string | null) => {
    if (!issueId) return;
    const newProject = projectId
      ? projectOptions.find((p) => p.id === projectId)
      : undefined;

    updateIssue(issueId, { project_id: projectId })
      .then(() => {
        toast.success(
          newProject ? `Project set to ${newProject.name}` : 'Project removed'
        );
      })
      .catch((error) => {
        toast.error('Failed to update project');
        console.error('Failed to update project:', error);
      });
  };

  const handleSetDueDate = () => {
    if (!issueId) return;
    const dueDate = new Date();
    dueDate.setDate(dueDate.getDate() + 7);
    updateIssue(issueId, { due_date: dueDate.toISOString() })
      .then(() => {
        toast.success('Due date set to 7 days from now');
      })
      .catch((error) => {
        toast.error('Failed to set due date');
        console.error('Failed to set due date:', error);
      });
  };

  const handleAddLink = () => {
    toast.success('Link added');
  };

  const handleMakeCopy = () => {
    toast.success('Issue copied');
  };

  const handleCreateRelated = () => {
    toast.success('Related issue created');
  };

  const handleMarkAs = (type: string) => {
    toast.success(`Marked as ${type}`);
  };

  const handleMove = () => {
    toast.success('Issue moved');
  };

  const handleSubscribe = () => {
    setIsSubscribed(!isSubscribed);
    toast.success(
      isSubscribed ? 'Unsubscribed from issue' : 'Subscribed to issue'
    );
  };

  const handleFavorite = () => {
    setIsFavorite(!isFavorite);
    toast.success(isFavorite ? 'Removed from favorites' : 'Added to favorites');
  };

  const handleCopy = () => {
    if (!issueId) return;
    const issue = getIssueById(issueId);
    if (issue) {
      navigator.clipboard.writeText(issue.title);
      toast.success('Copied to clipboard');
    }
  };

  const handleRemindMe = () => {
    toast.success('Reminder set');
  };

  return (
    <ContextMenuContent className='w-64'>
      <ContextMenuGroup>
        <ContextMenuSub>
          <ContextMenuSubTrigger>
            <CircleCheck className='mr-2 size-4' /> Status
          </ContextMenuSubTrigger>
          <ContextMenuSubContent className='w-48'>
            {statusOptions.map((s) => (
              <ContextMenuItem
                key={s.id}
                onClick={() => handleStatusChange(s.id)}
              >
                <div
                  className='w-3 h-3 rounded-full mr-2'
                  style={{ backgroundColor: s.color }}
                />
                {s.name}
              </ContextMenuItem>
            ))}
          </ContextMenuSubContent>
        </ContextMenuSub>

        <ContextMenuSub>
          <ContextMenuSubTrigger>
            <User className='mr-2 size-4' /> Assignee
          </ContextMenuSubTrigger>
          <ContextMenuSubContent className='w-48'>
            <ContextMenuItem onClick={() => handleAssigneeChange(null)}>
              <User className='size-4' /> Unassigned
            </ContextMenuItem>
            {userOptions.map((user) => (
              <ContextMenuItem
                key={user.id}
                onClick={() => handleAssigneeChange(user.id)}
              >
                <Avatar className='size-4'>
                  <AvatarImage
                    src={user.avatar_url || undefined}
                    alt={user.name}
                  />
                  <AvatarFallback>{user.name[0]}</AvatarFallback>
                </Avatar>
                {user.name}
              </ContextMenuItem>
            ))}
          </ContextMenuSubContent>
        </ContextMenuSub>

        <ContextMenuSub>
          <ContextMenuSubTrigger>
            <BarChart3 className='mr-2 size-4' /> Priority
          </ContextMenuSubTrigger>
          <ContextMenuSubContent className='w-48'>
            {priorityOptions.map((priority) => (
              <ContextMenuItem
                key={priority.id}
                onClick={() => handlePriorityChange(priority.id)}
              >
                <span className='mr-2 text-sm'>{priority.icon_name}</span>
                {priority.name}
              </ContextMenuItem>
            ))}
          </ContextMenuSubContent>
        </ContextMenuSub>

        <ContextMenuSub>
          <ContextMenuSubTrigger>
            <Tag className='mr-2 size-4' /> Labels
          </ContextMenuSubTrigger>
          <ContextMenuSubContent className='w-48'>
            {labelOptions.map((label) => (
              <ContextMenuItem
                key={label.id}
                onClick={() => handleLabelToggle(label.id)}
              >
                <span
                  className='inline-block size-3 rounded-full mr-2'
                  style={{ backgroundColor: getHexColor(label.color) }}
                  aria-hidden='true'
                />
                {label.name}
              </ContextMenuItem>
            ))}
          </ContextMenuSubContent>
        </ContextMenuSub>

        <ContextMenuSub>
          <ContextMenuSubTrigger>
            <Folder className='mr-2 size-4' /> Project
          </ContextMenuSubTrigger>
          <ContextMenuSubContent className='w-64'>
            <ContextMenuItem onClick={() => handleProjectChange(null)}>
              <Folder className='size-4' /> No Project
            </ContextMenuItem>
            {projectOptions.slice(0, 5).map((project) => (
              <ContextMenuItem
                key={project.id}
                onClick={() => handleProjectChange(project.id)}
              >
                <Folder className='size-4 mr-2' />
                {project.name}
              </ContextMenuItem>
            ))}
          </ContextMenuSubContent>
        </ContextMenuSub>

        <ContextMenuItem onClick={handleSetDueDate}>
          <CalendarClock className='size-4' /> Set due date...
          <ContextMenuShortcut>D</ContextMenuShortcut>
        </ContextMenuItem>

        <ContextMenuItem>
          <Pencil className='size-4' /> Rename...
          <ContextMenuShortcut>R</ContextMenuShortcut>
        </ContextMenuItem>

        <ContextMenuSeparator />

        <ContextMenuItem onClick={handleAddLink}>
          <LinkIcon className='size-4' /> Add link...
          <ContextMenuShortcut>Ctrl L</ContextMenuShortcut>
        </ContextMenuItem>

        <ContextMenuSub>
          <ContextMenuSubTrigger>
            <Repeat2 className='mr-2 size-4' /> Convert into
          </ContextMenuSubTrigger>
          <ContextMenuSubContent className='w-48'>
            <ContextMenuItem>
              <FileText className='size-4' /> Document
            </ContextMenuItem>
            <ContextMenuItem>
              <MessageSquare className='size-4' /> Comment
            </ContextMenuItem>
          </ContextMenuSubContent>
        </ContextMenuSub>

        <ContextMenuItem onClick={handleMakeCopy}>
          <CopyIcon className='size-4' /> Make a copy...
        </ContextMenuItem>
      </ContextMenuGroup>

      <ContextMenuSeparator />

      <ContextMenuItem onClick={handleCreateRelated}>
        <PlusSquare className='size-4' /> Create related
      </ContextMenuItem>

      <ContextMenuSub>
        <ContextMenuSubTrigger>
          <Flag className='mr-2 size-4' /> Mark as
        </ContextMenuSubTrigger>
        <ContextMenuSubContent className='w-48'>
          <ContextMenuItem onClick={() => handleMarkAs('Completed')}>
            <CheckCircle2 className='size-4' /> Completed
          </ContextMenuItem>
          <ContextMenuItem onClick={() => handleMarkAs('Duplicate')}>
            <CopyIcon className='size-4' /> Duplicate
          </ContextMenuItem>
          <ContextMenuItem onClick={() => handleMarkAs("Won't Fix")}>
            <Clock className='size-4' /> Won&apos;t Fix
          </ContextMenuItem>
        </ContextMenuSubContent>
      </ContextMenuSub>

      <ContextMenuItem onClick={handleMove}>
        <ArrowRightLeft className='size-4' /> Move
      </ContextMenuItem>

      <ContextMenuSeparator />

      <ContextMenuItem onClick={handleSubscribe}>
        <Bell className='size-4' /> {isSubscribed ? 'Unsubscribe' : 'Subscribe'}
        <ContextMenuShortcut>S</ContextMenuShortcut>
      </ContextMenuItem>

      <ContextMenuItem onClick={handleFavorite}>
        <Star className='size-4' /> {isFavorite ? 'Unfavorite' : 'Favorite'}
        <ContextMenuShortcut>F</ContextMenuShortcut>
      </ContextMenuItem>

      <ContextMenuItem onClick={handleCopy}>
        <Clipboard className='size-4' /> Copy
      </ContextMenuItem>

      <ContextMenuItem onClick={handleRemindMe}>
        <AlarmClock className='size-4' /> Remind me
        <ContextMenuShortcut>H</ContextMenuShortcut>
      </ContextMenuItem>

      <ContextMenuSeparator />

      <ContextMenuItem variant='destructive'>
        <Trash2 className='size-4' /> Delete...
        <ContextMenuShortcut>⌘⌫</ContextMenuShortcut>
      </ContextMenuItem>
    </ContextMenuContent>
  );
}
