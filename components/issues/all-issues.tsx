'use client';

import { type FC, useEffect, useMemo, useState } from 'react';
import { DndProvider } from 'react-dnd';
import { HTML5Backend } from 'react-dnd-html5-backend';

import { useIssues } from '@/hooks/use-db';
import { useFilterStore } from '@/lib/store/filter-store';
import { useSearchStore } from '@/lib/store/search-store';
import { useViewStore } from '@/lib/store/view-store';
import type { Issue } from '@/lib/supabase/database-modules';
import { cn } from '@/lib/utils';
import { GroupIssues } from './group-issues';
import { CustomDragLayer } from './issue-grid';
import { SearchIssues } from './search-issues';

export default function AllIssues() {
  const { isSearchOpen, searchQuery } = useSearchStore();
  const { viewType } = useViewStore();
  const { hasActiveFilters } = useFilterStore();

  const isSearching = isSearchOpen && searchQuery.trim() !== '';
  const isViewTypeGrid = viewType === 'grid';
  const isFiltering = hasActiveFilters();

  return (
    <div className={cn('w-full h-full', isViewTypeGrid && 'overflow-x-auto')}>
      {isSearching ? (
        <SearchIssuesView />
      ) : isFiltering ? (
        <FilteredIssuesView isViewTypeGrid={isViewTypeGrid} />
      ) : (
        <GroupIssuesListView isViewTypeGrid={isViewTypeGrid} />
      )}
    </div>
  );
}

const SearchIssuesView = () => (
  <div className='px-6 mb-6'>
    <SearchIssues />
  </div>
);

const FilteredIssuesView: FC<{
  isViewTypeGrid: boolean;
}> = ({ isViewTypeGrid = false }) => {
  const { filters } = useFilterStore();
  const { filterIssues } = useIssues();
  const [statusOptions, setStatusOptions] = useState<
    { id: string; name: string; color: string }[]
  >([]);

  // Load status options
  useEffect(() => {
    const loadStatusOptions = async () => {
      try {
        const response = await fetch('/api/status');
        if (response.ok) {
          const { status } = await response.json();
          setStatusOptions(status || []);
        }
      } catch (error) {
        console.error('Failed to load status options:', error);
      }
    };

    loadStatusOptions();
  }, []);

  // Get filtered issues using the hook's filter functionality
  const currentFilteredIssues = useMemo(() => {
    return filterIssues(filters);
  }, [filterIssues, filters]);

  // Group filtered issues by status
  const filteredIssuesByStatus = useMemo(() => {
    const result: Record<string, Issue[]> = {};

    statusOptions.forEach((statusItem) => {
      result[statusItem.id] = currentFilteredIssues.filter(
        (issue: Issue) => issue.status_id === statusItem.id
      );
    });

    return result;
  }, [currentFilteredIssues, statusOptions]);

  return (
    <DndProvider backend={HTML5Backend}>
      <CustomDragLayer />
      <div
        className={cn(
          isViewTypeGrid && 'flex h-full gap-3 px-2 py-2 min-w-max'
        )}
      >
        {statusOptions.map((statusItem) => (
          <GroupIssues
            key={statusItem.id}
            status={statusItem}
            issues={filteredIssuesByStatus[statusItem.id] || []}
            count={filteredIssuesByStatus[statusItem.id]?.length || 0}
          />
        ))}
      </div>
    </DndProvider>
  );
};

const GroupIssuesListView: FC<{
  isViewTypeGrid: boolean;
}> = ({ isViewTypeGrid = false }) => {
  const { issuesByStatus } = useIssues();
  const [statusOptions, setStatusOptions] = useState<
    { id: string; name: string; color: string }[]
  >([]);

  // Load status options
  useEffect(() => {
    const loadStatusOptions = async () => {
      try {
        const response = await fetch('/api/status');
        if (response.ok) {
          const { status } = await response.json();
          setStatusOptions(status || []);
        }
      } catch (error) {
        console.error('Failed to load status options:', error);
      }
    };

    loadStatusOptions();
  }, []);

  return (
    <DndProvider backend={HTML5Backend}>
      <CustomDragLayer />
      <div
        className={cn(
          isViewTypeGrid && 'flex h-full gap-3 px-2 py-2 min-w-max'
        )}
      >
        {statusOptions.map((statusItem) => (
          <GroupIssues
            key={statusItem.id}
            status={statusItem}
            issues={issuesByStatus[statusItem.id] || []}
            count={issuesByStatus[statusItem.id]?.length || 0}
          />
        ))}
      </div>
    </DndProvider>
  );
};
