'use client';

import { create } from 'zustand';

interface CreateIssueState {
  isOpen: boolean;
  isLoading: boolean;
  defaultStatus?: string;
  setIsOpen: (isOpen: boolean) => void;
  setIsLoading: (isLoading: boolean) => void;
  setDefaultStatus: (status: string) => void;
  openDialog: () => void;
  closeDialog: () => void;
  openModal: () => void;
  closeModal: () => void;
}

export const useCreateIssueStore = create<CreateIssueState>((set) => ({
  isOpen: false,
  isLoading: false,
  defaultStatus: undefined,

  setIsOpen: (isOpen: boolean) => set({ isOpen }),

  setIsLoading: (isLoading: boolean) => set({ isLoading }),

  setDefaultStatus: (status: string) => set({ defaultStatus: status }),

  openDialog: () => set({ isOpen: true }),

  closeDialog: () => set({ isOpen: false, isLoading: false }),

  openModal: () => set({ isOpen: true }),

  closeModal: () => set({ isOpen: false, isLoading: false }),
}));
