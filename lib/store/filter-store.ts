'use client';

import { create } from 'zustand';
import type { FilterOptions } from '@/lib/supabase/database-modules';

interface FilterState {
  filters: FilterOptions;
  setFilters: (filters: FilterOptions) => void;
  updateFilter: <K extends keyof FilterOptions>(
    key: K,
    value: FilterOptions[K]
  ) => void;
  clearFilters: () => void;
  hasActiveFilters: () => boolean;
}

const defaultFilters: FilterOptions = {
  status: [],
  priority: [],
  assignee: [],
  project: [],
  labels: [],
};

export const useFilterStore = create<FilterState>((set, get) => ({
  filters: defaultFilters,

  setFilters: (filters: FilterOptions) => set({ filters }),

  updateFilter: <K extends keyof FilterOptions>(
    key: K,
    value: FilterOptions[K]
  ) =>
    set((state) => ({
      filters: {
        ...state.filters,
        [key]: value,
      },
    })),

  clearFilters: () => set({ filters: defaultFilters }),

  hasActiveFilters: () => {
    const { filters } = get();
    return Object.values(filters).some((filter) =>
      Array.isArray(filter) ? filter.length > 0 : <PERSON><PERSON><PERSON>(filter)
    );
  },
}));
