import type { LucideIcon } from 'lucide-react';
import type { Database } from './database-types';

type WorkTypes =
  | 'Graphic Design'
  | 'Website Development'
  | 'App Development'
  | 'Brand Development';

type VolunteerAreaTypes =
  | 'Design (Graphic/Web)'
  | 'Content Creation (Social Media, Blog)'
  | 'Event Support'
  | 'Community Engagement & Outreach'
  | 'Branding Strategy'
  | 'Photography/Videography'
  | 'Other (Please Specify)';

type VolunteerAvailabilityTypes =
  | 'Less than 5 hours'
  | '5-10 hours'
  | '10-15 hours'
  | '15+ hours';

type VolunteerDaysAvailabilityTypes =
  | 'Monday'
  | 'Tuesday'
  | 'Wednesday'
  | 'Thursday'
  | 'Friday'
  | 'Saturday'
  | 'Sunday';

type VolunteerTimeOfTheDayAvailabilityTypes =
  | 'Morning'
  | 'Afternoon'
  | 'Evening';

export type ReferalProposalType = {
  proposal_type: string | WorkTypes;
  client_name: string;
  proposal_message: string;
};

export type VolunteerDetailsType = {
  dob: string;
  about_you: string;
  interest_in_volunteering: string;
  relivant_skills: string;
  contribution_area: string | VolunteerAreaTypes;
  reason_for_joining: string;
  past_volunteering: string;
  hours_per_week: VolunteerAvailabilityTypes;
  days_per_week: VolunteerDaysAvailabilityTypes[];
  prefered_time_of_day: VolunteerTimeOfTheDayAvailabilityTypes;
  access_to_equipments: boolean;
  other_details: string;
  subscribe_to_newsletter: boolean;
};

export type Profile_Types = Database['public']['Tables']['profiles']['Row'];
export type UserRole = Database['public']['Enums']['Role Types'];

export interface User {
  name: Profile_Types['full_name'];
  email: Profile_Types['email'];
  avatar: string;
  role: Profile_Types['role'];
}

export interface SidebarData {
  user: User;
  teams: Array<{
    name: string;
    logo: LucideIcon;
    plan: string;
  }>;
  navHeader: Array<{
    title: string;
    url: string;
    icon: LucideIcon;
  }>;
  navMain: Array<{
    title: string;
    items?: Array<{
      icon: LucideIcon;
      title: string;
      isActive?: boolean;
      url: string;
      items?: Array<{
        icon: LucideIcon;
        title: string;
        url: string;
      }>;
    }>;
  }>;
}

// ============================================================================
// Issues Management System Types
// ============================================================================

// Base types for issues system
export interface IssueUser {
  id: string;
  name: string;
  email: string;
  avatar_url: string | null;
  status: 'online' | 'offline' | 'away';
  role: 'Member' | 'Admin' | 'Guest';
  joined_date: string;
}

export interface IssueStatus {
  id: string;
  name: string;
  color: string;
  icon_name: string | null;
  sort_order: number;
}

export interface IssuePriority {
  id: string;
  name: string;
  icon_name: string | null;
  sort_order: number;
}

export interface IssueLabel {
  id: string;
  name: string;
  color: string;
}

// Label Management System Types
export interface Label {
  id: string;
  name: string;
  color: string;
  created_at: string | null;
}

// Priority Management System Types
export interface Priority {
  id: string;
  name: string;
  icon_name: string | null;
  sort_order: number | null;
  created_at: string | null;
}

// Status Management System Types
export interface Status {
  id: string;
  name: string;
  color: string;
  icon_name: string | null;
  sort_order: number | null;
  created_at: string | null;
}

// Project Management System Types
export interface Project {
  id: string;
  name: string;
  description: string | null;
  icon: string | null;
  percent_complete: number;
  start_date: string | null;
  target_date: string | null;
  lead_id: string | null;
  status_id: string | null;
  priority_id: string | null;
  health_id: 'no-update' | 'off-track' | 'on-track' | 'at-risk' | null;
  created_at: string;
  updated_at: string;
  created_by: string | null;
  is_archived: boolean;
}

export interface CreateProjectInput {
  name: string;
  description?: string | null;
  icon?: string | null;
  percent_complete?: number;
  start_date?: string | null;
  target_date?: string | null;
  lead_id?: string | null;
  status_id?: string | null;
  priority_id?: string | null;
  health_id?: 'no-update' | 'off-track' | 'on-track' | 'at-risk' | null;
}

export interface IssueProject {
  id: string;
  name: string;
  description: string | null;
  icon: string | null;
  percent_complete: number;
  start_date: string | null;
  target_date: string | null;
  lead_id: string | null;
  status_id: string | null;
  priority_id: string | null;
  health_id: 'no-update' | 'off-track' | 'on-track' | 'at-risk' | null;
  lead?: IssueUser;
  status?: IssueStatus;
  priority?: IssuePriority;
}

export interface IssueCycle {
  id: string;
  number: number;
  name: string;
  team_id: string;
  start_date: string;
  end_date: string;
  progress: number;
}

export interface Issue {
  id: string;
  identifier: string;
  title: string;
  description: string;
  status_id: string;
  assignee_id: string | null;
  priority_id: string;
  project_id: string | null;
  cycle_id: string | null;
  parent_issue_id: string | null;
  rank: string;
  due_date: string | null;
  created_at: string;
  updated_at: string;
  created_by: string;
  // Joined data
  status?: IssueStatus;
  assignee?: IssueUser;
  priority?: IssuePriority;
  project?: IssueProject;
  cycle?: IssueCycle;
  labels?: IssueLabel[];
  subissues?: string[];
}

export interface CreateIssueInput {
  title: string;
  description?: string;
  status_id: string;
  assignee_id?: string | null;
  priority_id: string;
  project_id?: string | null;
  cycle_id?: string | null;
  parent_issue_id?: string | null;
  rank: string;
  due_date?: string | null;
  labels?: string[];
}

export interface FilterOptions {
  status?: string[];
  assignee?: string[];
  priority?: string[];
  labels?: string[];
  project?: string[];
}

export interface IssuesState {
  issues: Issue[];
  issuesByStatus: Record<string, Issue[]>;
  loading: boolean;
  error: string | null;
}

// Database table types using the Database schema
export type DatabaseIssue = Database['public']['Tables']['issues']['Row'];
export type DatabaseStatus = Database['public']['Tables']['status']['Row'];
export type DatabasePriority =
  Database['public']['Tables']['priorities']['Row'];
export type DatabaseLabel = Database['public']['Tables']['labels']['Row'];
export type DatabaseProject = Database['public']['Tables']['projects']['Row'];

// Insert and Update types
export type CreateIssueDatabase =
  Database['public']['Tables']['issues']['Insert'];
export type UpdateIssueDatabase =
  Database['public']['Tables']['issues']['Update'];

// ============================================================================
// Teams Management System Types
// ============================================================================

export interface Team {
  id: string;
  name: string;
  icon: string | null;
  color: string | null;
  description: string | null;
  created_at: string | null;
  updated_at: string | null;
}

export interface CreateTeamInput {
  id: string;
  name: string;
  icon?: string | null;
  color?: string | null;
  description?: string | null;
}

export interface TeamMember {
  id: string;
  team_id: string | null;
  user_id: string | null;
  joined: boolean | null;
  joined_at: string | null;
}

export interface CreateTeamMemberInput {
  team_id: string;
  user_id: string;
  joined?: boolean;
}

// ============================================================================
// Cycles Management System Types
// ============================================================================

export interface Cycle {
  id: string;
  number: number;
  name: string;
  team_id: string | null;
  start_date: string;
  end_date: string;
  progress: number | null;
  created_at: string | null;
  updated_at: string | null;
}

export interface CreateCycleInput {
  id: string;
  number: number;
  name: string;
  team_id?: string | null;
  start_date: string;
  end_date: string;
  progress?: number;
}

// ============================================================================
// Payments Management System Types
// ============================================================================

export interface Payment {
  id: string;
  amount: number;
  currency: string;
  status: 'pending' | 'completed' | 'failed' | 'cancelled';
  payment_method: string | null;
  transaction_id: string | null;
  user_id: string | null;
  project_id: string | null;
  invoice_id: string | null;
  description: string | null;
  metadata: Record<string, unknown> | null;
  created_at: string;
  updated_at: string;
  created_by: string | null;
}

export interface CreatePaymentInput {
  amount: number;
  currency: string;
  status?: 'pending' | 'completed' | 'failed' | 'cancelled';
  payment_method?: string | null;
  transaction_id?: string | null;
  user_id?: string | null;
  project_id?: string | null;
  invoice_id?: string | null;
  description?: string | null;
  metadata?: Record<string, unknown> | null;
}

// ============================================================================
// Proposals Management System Types
// ============================================================================

export interface Proposal {
  id: number;
  created_at: string;
  user_id: string | null;
  user_email: string | null;
  affiliate_proposal: ReferalProposalType | null;
  is_recieved: boolean | null;
  is_approved: boolean | null;
  completed: boolean | null;
}

export interface CreateProposalInput {
  user_id?: string | null;
  user_email?: string | null;
  affiliate_proposal?: ReferalProposalType | null;
  is_recieved?: boolean;
  is_approved?: boolean;
  completed?: boolean;
}

// ============================================================================
// Invoices Management System Types
// ============================================================================

export interface Invoice {
  id: string;
  invoice_number: string;
  user_id: string | null;
  project_id: string | null;
  amount: number;
  tax_amount: number | null;
  total_amount: number;
  status: 'draft' | 'sent' | 'paid' | 'overdue' | 'cancelled';
  issue_date: string;
  due_date: string;
  paid_date: string | null;
  notes: string | null;
  created_at: string;
  updated_at: string;
  created_by: string | null;
  is_archived: boolean;
}

export interface CreateInvoiceInput {
  invoice_number: string;
  user_id?: string | null;
  project_id?: string | null;
  amount: number;
  tax_amount?: number | null;
  total_amount: number;
  status?: 'draft' | 'sent' | 'paid' | 'overdue' | 'cancelled';
  due_date: string;
  notes?: string | null;
}

// ============================================================================
// Transactions Management System Types
// ============================================================================

export interface Transaction {
  id: string;
  type: 'income' | 'expense' | 'transfer';
  amount: number;
  currency: string;
  description: string | null;
  category: string | null;
  reference_id: string | null;
  status: 'pending' | 'completed' | 'failed';
  metadata: Record<string, unknown> | null;
  created_at: string;
  updated_at: string;
  created_by: string | null;
}

export interface CreateTransactionInput {
  type: 'income' | 'expense' | 'transfer';
  amount: number;
  currency: string;
  description?: string | null;
  category?: string | null;
  reference_id?: string | null;
  status?: 'pending' | 'completed' | 'failed';
  metadata?: Record<string, unknown> | null;
}

// ============================================================================
// Affiliate Earnings Management System Types
// ============================================================================

export interface AffiliateEarning {
  id: string;
  user_id: string;
  amount: number;
  commission_rate: number;
  source: string;
  reference_id: string | null;
  earned_date: string;
  status: string;
  paid_date: string | null;
  payment_id: string | null;
  created_at: string;
  updated_at: string;
}

export interface CreateAffiliateEarningInput {
  user_id: string;
  amount: number;
  commission_rate: number;
  source: string;
  reference_id?: string | null;
  status?: string;
}
