/**
 * Utility functions for handling colors in the application
 */

// Color name to hex mapping for common colors used in labels
const COLOR_NAME_TO_HEX: Record<string, string> = {
  // Basic colors
  red: '#ef4444',
  green: '#22c55e',
  blue: '#3b82f6',
  yellow: '#eab308',
  orange: '#f97316',
  purple: '#a855f7',
  pink: '#ec4899',
  indigo: '#6366f1',
  cyan: '#06b6d4',
  gray: '#6b7280',
  grey: '#6b7280',
  
  // Extended colors
  lime: '#84cc16',
  emerald: '#10b981',
  teal: '#14b8a6',
  sky: '#0ea5e9',
  violet: '#8b5cf6',
  fuchsia: '#d946ef',
  rose: '#f43f5e',
  slate: '#64748b',
  zinc: '#71717a',
  neutral: '#737373',
  stone: '#78716c',
  
  // Common variations
  black: '#000000',
  white: '#ffffff',
  dark: '#1f2937',
  light: '#f9fafb',
};

/**
 * Converts a color name to hex value, or returns the original value if it's already a hex color
 * @param color - Color name (e.g., 'red') or hex value (e.g., '#ff0000')
 * @returns Hex color value
 */
export function getHexColor(color: string): string {
  // If it's already a hex color (starts with #), return as is
  if (color.startsWith('#')) {
    return color;
  }
  
  // Convert to lowercase for case-insensitive matching
  const normalizedColor = color.toLowerCase().trim();
  
  // Return mapped hex value or fallback to a default color
  return COLOR_NAME_TO_HEX[normalizedColor] || '#6b7280'; // Default to gray
}

/**
 * Checks if a string is a valid hex color
 * @param color - Color string to validate
 * @returns True if valid hex color, false otherwise
 */
export function isHexColor(color: string): boolean {
  return /^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/.test(color);
}

/**
 * Converts hex color to RGB values
 * @param hex - Hex color string (e.g., '#ff0000')
 * @returns RGB object with r, g, b values
 */
export function hexToRgb(hex: string): { r: number; g: number; b: number } | null {
  const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
  return result
    ? {
        r: parseInt(result[1], 16),
        g: parseInt(result[2], 16),
        b: parseInt(result[3], 16),
      }
    : null;
}

/**
 * Gets a contrasting text color (black or white) for a given background color
 * @param backgroundColor - Background color in hex format
 * @returns '#000000' for light backgrounds, '#ffffff' for dark backgrounds
 */
export function getContrastingTextColor(backgroundColor: string): string {
  const hex = getHexColor(backgroundColor);
  const rgb = hexToRgb(hex);
  
  if (!rgb) return '#000000';
  
  // Calculate luminance
  const luminance = (0.299 * rgb.r + 0.587 * rgb.g + 0.114 * rgb.b) / 255;
  
  // Return black for light backgrounds, white for dark backgrounds
  return luminance > 0.5 ? '#000000' : '#ffffff';
}
