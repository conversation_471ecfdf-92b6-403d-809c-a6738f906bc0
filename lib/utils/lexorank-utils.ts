/**
 * LexoRank Utilities for Issue Ordering
 *
 * LexoRank is a ranking system that allows for efficient ordering of items
 * in a list without having to update all items when inserting between two items.
 *
 * This implementation provides utilities for generating and managing LexoRank
 * strings for issue ordering in drag-and-drop scenarios.
 */

// Constants for LexoRank
const MIN_CHAR = '0';
const MAX_CHAR = 'z';
const MID_CHAR = 'U';

// LexoRank interface
interface LexoRankInstance {
  value: string;
  toString(): string;
  next(): LexoRankInstance;
  prev(): LexoRankInstance;
  between(other: LexoRankInstance): LexoRankInstance;
  compareTo(other: LexoRankInstance): number;
  equals(other: LexoRankInstance): boolean;
}

// Helper functions
const increment = (value: string): string => {
  const chars = value.split('');
  let carry = true;

  for (let i = chars.length - 1; i >= 0 && carry; i--) {
    const charCode = chars[i].charCodeAt(0);

    if (charCode < MAX_CHAR.charCodeAt(0)) {
      chars[i] = String.fromCharCode(charCode + 1);
      carry = false;
    } else {
      chars[i] = MIN_CHAR;
    }
  }

  if (carry) {
    return MIN_CHAR + chars.join('');
  }

  return chars.join('');
};

const decrement = (value: string): string => {
  const chars = value.split('');
  let borrow = true;

  for (let i = chars.length - 1; i >= 0 && borrow; i--) {
    const charCode = chars[i].charCodeAt(0);

    if (charCode > MIN_CHAR.charCodeAt(0)) {
      chars[i] = String.fromCharCode(charCode - 1);
      borrow = false;
    } else {
      chars[i] = MAX_CHAR;
    }
  }

  return chars.join('');
};

const generateBetween = (prev: string, next: string): string => {
  const maxLength = Math.max(prev.length, next.length);
  const prevPadded = prev.padEnd(maxLength, MIN_CHAR);
  const nextPadded = next.padEnd(maxLength, MIN_CHAR);

  let result = '';
  let carry = 0;

  for (let i = 0; i < maxLength; i++) {
    const prevChar = prevPadded.charCodeAt(i);
    const nextChar = nextPadded.charCodeAt(i);
    const mid = Math.floor((prevChar + nextChar + carry) / 2);

    result += String.fromCharCode(mid);

    if (mid === prevChar) {
      carry = 1;
    } else {
      carry = 0;
      break;
    }
  }

  // If we couldn't find a middle value, append a character
  if (carry === 1) {
    result += MID_CHAR;
  }

  return result;
};

// Create LexoRank instance
const createLexoRank = (value: string = MID_CHAR): LexoRankInstance => ({
  value,

  toString(): string {
    return this.value;
  },

  next(): LexoRankInstance {
    return createLexoRank(increment(this.value));
  },

  prev(): LexoRankInstance {
    return createLexoRank(decrement(this.value));
  },

  between(other: LexoRankInstance): LexoRankInstance {
    const thisValue = this.value;
    const otherValue = other.value;

    if (thisValue >= otherValue) {
      throw new Error(
        'Invalid rank order: first rank must be less than second rank'
      );
    }

    return createLexoRank(generateBetween(thisValue, otherValue));
  },

  compareTo(other: LexoRankInstance): number {
    return this.value.localeCompare(other.value);
  },

  equals(other: LexoRankInstance): boolean {
    return this.value === other.value;
  },
});

export const LexoRank = {
  /**
   * Create a LexoRank from a string value
   */
  from: (value: string): LexoRankInstance => {
    return createLexoRank(value);
  },

  /**
   * Generate the first rank (minimum)
   */
  min: (): LexoRankInstance => {
    return createLexoRank(MIN_CHAR);
  },

  /**
   * Generate the last rank (maximum)
   */
  max: (): LexoRankInstance => {
    return createLexoRank(MAX_CHAR);
  },

  /**
   * Generate a middle rank
   */
  middle: (): LexoRankInstance => {
    return createLexoRank(MID_CHAR);
  },

  /**
   * Generate a rank between two existing ranks
   */
  between: (
    prev: LexoRankInstance | null,
    next: LexoRankInstance | null
  ): LexoRankInstance => {
    if (!prev && !next) {
      return LexoRank.middle();
    }

    if (!prev && next) {
      return LexoRank.from(next.value).prev();
    }

    if (!next && prev) {
      return LexoRank.from(prev.value).next();
    }

    if (prev && next) {
      return LexoRank.from(prev.value).between(LexoRank.from(next.value));
    }

    // Fallback to middle if both are null (shouldn't happen due to first check)
    return LexoRank.middle();
  },
};

/**
 * Utility functions for working with LexoRank in the context of issues
 */
export const IssueRankUtils = {
  /**
   * Generate a rank for a new issue at the top of a status column
   */
  getTopRank: (existingIssues: Array<{ rank: string }>): string => {
    if (existingIssues.length === 0) {
      return LexoRank.middle().toString();
    }

    // Sort by rank to find the highest
    const sorted = existingIssues
      .map((issue) => LexoRank.from(issue.rank))
      .sort((a, b) => b.compareTo(a));

    const topRank = sorted[0];
    return topRank.next().toString();
  },

  /**
   * Generate a rank for a new issue at the bottom of a status column
   */
  getBottomRank: (existingIssues: Array<{ rank: string }>): string => {
    if (existingIssues.length === 0) {
      return LexoRank.middle().toString();
    }

    // Sort by rank to find the lowest
    const sorted = existingIssues
      .map((issue) => LexoRank.from(issue.rank))
      .sort((a, b) => a.compareTo(b));

    const bottomRank = sorted[0];
    return bottomRank.prev().toString();
  },

  /**
   * Generate a rank for inserting an issue between two others
   */
  getRankBetween: (
    prevIssue: { rank: string } | null,
    nextIssue: { rank: string } | null
  ): string => {
    const prevRank = prevIssue ? LexoRank.from(prevIssue.rank) : null;
    const nextRank = nextIssue ? LexoRank.from(nextIssue.rank) : null;

    return LexoRank.between(prevRank, nextRank).toString();
  },

  /**
   * Sort issues by their rank
   */
  sortByRank: <T extends { rank: string }>(issues: T[]): T[] => {
    return issues.sort((a, b) => {
      const rankA = LexoRank.from(a.rank);
      const rankB = LexoRank.from(b.rank);
      return rankB.compareTo(rankA); // Descending order (newest first)
    });
  },

  /**
   * Generate a rank for moving an issue to a specific position in a list
   */
  getRankForPosition: (
    issues: Array<{ rank: string }>,
    targetIndex: number
  ): string => {
    const sortedIssues = IssueRankUtils.sortByRank(issues);

    if (targetIndex <= 0) {
      return IssueRankUtils.getTopRank(sortedIssues);
    }

    if (targetIndex >= sortedIssues.length) {
      return IssueRankUtils.getBottomRank(sortedIssues);
    }

    const prevIssue = sortedIssues[targetIndex - 1];
    const nextIssue = sortedIssues[targetIndex];

    return IssueRankUtils.getRankBetween(prevIssue, nextIssue);
  },

  /**
   * Validate that a rank string is valid
   */
  isValidRank: (rank: string): boolean => {
    if (!rank || typeof rank !== 'string') {
      return false;
    }

    // Check that all characters are valid
    for (const char of rank) {
      const code = char.charCodeAt(0);
      if (code < MIN_CHAR.charCodeAt(0) || code > MAX_CHAR.charCodeAt(0)) {
        return false;
      }
    }

    return true;
  },

  /**
   * Generate initial ranks for a batch of issues
   */
  generateInitialRanks: (count: number): string[] => {
    const ranks: string[] = [];
    let currentRank = LexoRank.middle();

    for (let i = 0; i < count; i++) {
      ranks.push(currentRank.toString());
      currentRank = currentRank.next();
    }

    return ranks;
  },
};

/**
 * Example usage:
 *
 * // Create initial rank
 * const firstRank = LexoRank.middle().toString(); // "U"
 *
 * // Create rank for new item at top
 * const topRank = LexoRank.from(firstRank).next().toString(); // "V"
 *
 * // Create rank between two items
 * const betweenRank = LexoRank.between(
 *   LexoRank.from("U"),
 *   LexoRank.from("V")
 * ).toString(); // "UU" (approximately)
 *
 * // For drag and drop scenarios:
 * const newRank = IssueRankUtils.getRankBetween(
 *   { rank: "U" }, // previous issue
 *   { rank: "V" }  // next issue
 * ); // Returns rank between U and V
 */
