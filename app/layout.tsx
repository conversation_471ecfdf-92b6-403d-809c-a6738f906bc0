import type { Viewport } from 'next';
import './globals.css';
import { ThemeProvider } from '@/components/providers/theme-provider';
import { Toaster } from '@/components/ui/sonner';
import { FONT_CHAKRA_PETCH } from '@/lib/constants/fonts';
import { cn, constructMetadata } from '@/lib/utils';

export const metadata = constructMetadata({
  title: 'Team - thehuefactory™',
});

export const viewport: Viewport = {
  themeColor: '#f5f5f5',
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang='en' suppressHydrationWarning>
      <body className={cn('tracking-wider ', FONT_CHAKRA_PETCH.className)}>
        <ThemeProvider
          attribute='class'
          defaultTheme='system'
          enableSystem
          disableTransitionOnChange
        >
          {children}
        </ThemeProvider>
        <Toaster />
      </body>
    </html>
  );
}
