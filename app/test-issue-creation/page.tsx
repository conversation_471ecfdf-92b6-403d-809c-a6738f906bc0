'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { useIssues } from '@/hooks/use-db';
import { supabaseClient } from '@/lib/supabase/auth/client';
import type { CreateIssueInput } from '@/lib/supabase/database-modules';
import { toast } from 'sonner';

export default function TestIssueCreationPage() {
  const { addIssue } = useIssues();
  const [loading, setLoading] = useState(false);
  const [authInfo, setAuthInfo] = useState<any>(null);
  const [formData, setFormData] = useState({
    title: 'Test Issue',
    description: 'This is a test issue created from the test page',
    status_id: 'to-do',
    priority_id: 'medium',
  });

  const checkAuth = async () => {
    try {
      const { data: { user }, error } = await supabaseClient.auth.getUser();
      setAuthInfo({ user, error });
      console.log('Auth check result:', { user, error });
    } catch (error) {
      console.error('Auth check failed:', error);
      setAuthInfo({ error });
    }
  };

  const testCreateIssue = async () => {
    setLoading(true);
    try {
      const issueInput: CreateIssueInput = {
        title: formData.title,
        description: formData.description,
        status_id: formData.status_id,
        priority_id: formData.priority_id,
        rank: 'a0', // Simple rank for testing
      };

      console.log('Creating issue with input:', issueInput);
      
      const result = await addIssue(issueInput);
      console.log('Issue created successfully:', result);
      toast.success('Issue created successfully!');
    } catch (error) {
      console.error('Failed to create issue:', error);
      toast.error(`Failed to create issue: ${error instanceof Error ? error.message : 'Unknown error'}`);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="container mx-auto p-6 max-w-2xl">
      <h1 className="text-2xl font-bold mb-6">Test Issue Creation</h1>
      
      <div className="space-y-6">
        {/* Authentication Status */}
        <Card>
          <CardHeader>
            <CardTitle>Authentication Status</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <Button onClick={checkAuth}>Check Authentication</Button>
            {authInfo && (
              <div className="bg-gray-100 p-4 rounded-md">
                <pre className="text-sm overflow-auto">
                  {JSON.stringify(authInfo, null, 2)}
                </pre>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Issue Creation Test */}
        <Card>
          <CardHeader>
            <CardTitle>Create Test Issue</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <Label htmlFor="title">Title</Label>
              <Input
                id="title"
                value={formData.title}
                onChange={(e) => setFormData(prev => ({ ...prev, title: e.target.value }))}
              />
            </div>
            
            <div>
              <Label htmlFor="description">Description</Label>
              <Textarea
                id="description"
                value={formData.description}
                onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
              />
            </div>
            
            <div>
              <Label htmlFor="status">Status ID</Label>
              <Input
                id="status"
                value={formData.status_id}
                onChange={(e) => setFormData(prev => ({ ...prev, status_id: e.target.value }))}
              />
            </div>
            
            <div>
              <Label htmlFor="priority">Priority ID</Label>
              <Input
                id="priority"
                value={formData.priority_id}
                onChange={(e) => setFormData(prev => ({ ...prev, priority_id: e.target.value }))}
              />
            </div>
            
            <Button 
              onClick={testCreateIssue} 
              disabled={loading}
              className="w-full"
            >
              {loading ? 'Creating Issue...' : 'Create Test Issue'}
            </Button>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
