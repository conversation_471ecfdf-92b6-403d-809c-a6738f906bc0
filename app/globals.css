@import "tailwindcss";
@import "tw-animate-css";

@custom-variant dark (&:is(.dark *));

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
  --color-sidebar-ring: var(--sidebar-ring);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar: var(--sidebar);
  --color-chart-5: var(--chart-5);
  --color-chart-4: var(--chart-4);
  --color-chart-3: var(--chart-3);
  --color-chart-2: var(--chart-2);
  --color-chart-1: var(--chart-1);
  --color-ring: var(--ring);
  --color-input: var(--input);
  --color-border: var(--border);
  --color-destructive: var(--destructive);
  --color-accent-foreground: var(--accent-foreground);
  --color-accent: var(--accent);
  --color-muted-foreground: var(--muted-foreground);
  --color-muted: var(--muted);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-secondary: var(--secondary);
  --color-primary-foreground: var(--primary-foreground);
  --color-primary: var(--primary);
  --color-popover-foreground: var(--popover-foreground);
  --color-popover: var(--popover);
  --color-card-foreground: var(--card-foreground);
  --color-card: var(--card);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
}

@theme {
  /* Custom brand colors */
  --color-accent-50: #ffddd1;
  --color-accent-100: #ff4200;
  --color-accent-200: #d53700;
  --color-accent-300: #7f2100;
  --color-accent-400: #3e1000;
  --color-black-alt: #0A0A0A;

  /* Brand colors - light mode */
  --color-brand-100: #ffd9cc;
  --color-brand-200: #ff8399;
  --color-brand-300: #ff5e60;
  --color-brand-400: #ff6833;
  --color-brand-500: #ff4200;
  --color-brand-600: #cc3500;
  --color-brand-700: #992800;
  --color-brand-800: #661a00;
  --color-brand-900: #330d00;

  /* Brand colors - dark mode */
  --color-branddark-100: #330d00;
  --color-branddark-200: #661a00;
  --color-branddark-300: #992800;
  --color-branddark-400: #cc3500;
  --color-branddark-500: #ff4200;
  --color-branddark-600: #ff6800;
  --color-branddark-700: #ff8e66;
  --color-branddark-800: #ffb399;
  --color-branddark-900: #ffd9cc;

  /* Animations */
  --animate-accordion-down: accordion-down 0.2s ease-out;
  --animate-accordion-up: accordion-up 0.2s ease-out;
  --animate-marquee: marquee var(--duration) linear infinite;
  --animate-marquee-vertical: marquee-vertical var(--duration) linear infinite;
  --animate-progress-active: progressFill 5s linear forwards;

  @keyframes accordion-down {
    from {
      height: 0;
    }

    to {
      height: var(--radix-accordion-content-height);
    }
  }

  @keyframes accordion-up {
    from {
      height: var(--radix-accordion-content-height);
    }

    to {
      height: 0;
    }
  }

  @keyframes marquee {
    from {
      transform: translateX(0);
    }

    to {
      transform: translateX(calc(-100% - var(--gap)));
    }
  }

  @keyframes marquee-vertical {
    from {
      transform: translateY(0);
    }

    to {
      transform: translateY(calc(-100% - var(--gap)));
    }
  }

  @keyframes progressFill {
    from {
      width: 0%;
    }

    to {
      width: 100%;
    }
  }
}

/* Container utility */
@utility container {
  margin-inline: auto;
  padding-inline: 2rem;

  @media (width >=--theme(--breakpoint-sm)) {
    max-width: none;
  }

  @media (width >=1400px) {
    max-width: 1400px;
  }
}

/* Background utilities */
@utility home-bg-gradient {
  background-color: rgb(249, 249, 249);
  background-repeat: no-repeat;
  background-image: url('/assets/imgs/slider/elements/transparent.png');
  background-size: cover;
  background-position: center center;
  width: 100%;
  height: 100%;
  opacity: 1;
  visibility: inherit;
  z-index: 20;
}

@utility join-us-bg {
  background-color: rgb(249, 249, 249);
  background-repeat: no-repeat;
  background-image: url('/assets/imgs/bg/space.png');
  background-size: cover;
  background-position: center center;
  width: 100%;
  height: 100%;
  opacity: 1;
  visibility: inherit;
  z-index: 20;
}

@utility waitlist-sky-bg {
  background-color: rgb(249, 249, 249);
  background-repeat: no-repeat;
  background-image: url('/assets/imgs/bg/SKY.jpg');
  background-size: cover;
  background-position: center center;
  width: 100%;
  height: 100%;
  opacity: 1;
  visibility: inherit;
  z-index: 20;
}

@utility waitlist-grass-bg {
  background-repeat: no-repeat;
  background-image: url('/assets/svgs/GRASS.svg');
  background-size: cover;
  background-position: bottom center;
  width: 100%;
  height: auto;
  opacity: 1;
  visibility: inherit;
  z-index: 20;
}

@utility content_bg_h_desktop {
  background-color: rgb(249, 249, 249);
  background-repeat: no-repeat;
  background-image: url('/assets/imgs/content/content_h.png');
  background-size: cover;
  background-position: top left;
  width: 100%;
  height: 100%;
  opacity: 1;
  visibility: inherit;
  z-index: 20;
}

@utility content_bg_h_mobile {
  background-color: rgb(249, 249, 249);
  background-repeat: no-repeat;
  background-image: url('/assets/imgs/content/content-bg.png');
  background-size: cover;
  object-position: left top;
  background-position: center center;
  width: 100%;
  height: 100%;
  opacity: 1;
  visibility: inherit;
  z-index: 20;
}

@utility content_bg_w_desktop {
  background-color: rgb(249, 249, 249);
  background-repeat: no-repeat;
  background-image: url('/assets/imgs/content/content_w.png');
  background-size: cover;
  background-position: center right;
  width: 100%;
  height: 100%;
  opacity: 1;
  visibility: inherit;
  z-index: 20;
}

@utility content_bg_w_mobile {
  background-color: rgb(249, 249, 249);
  background-repeat: no-repeat;
  background-image: url('/assets/imgs/content/content-bg-2.png');
  background-size: cover;
  object-position: left top;
  background-position: top right;
  width: 100%;
  height: 100%;
  opacity: 1;
  visibility: inherit;
  z-index: 20;
}

@utility parallax {
  height: 48rem;
  background-attachment: fixed;
  background-position: center;
  background-repeat: no-repeat;
  background-size: cover;
  background-image: url('/assets/imgs/bg/pricing.jpg');
}

/* Button shadow utilities */
@utility shadow-dark {
  background-color: var(--btn-dark-color);
  box-shadow:
    var(--btn-dark-shadow-inner) 0px -2.4px 0px 0px inset,
    rgba(143, 39, 9, 0.2) 0px 1px 3px 0px,
    var(--btn-dark-shadow-outer) 0px 0px 0px 1px;
  opacity: 1;

  &:hover {
    background-color: var(--btn-dark-hover-color);
    box-shadow:
      var(--btn-dark-shadow-inner) 0px 0px 0px 0px inset,
      rgba(143, 39, 9, 0.2) 0px 1px 3px 0px,
      var(--btn-dark-shadow-outer) 0px 0px 0px 1px;
    opacity: 1;
  }
}

@utility shadow-light {
  background-color: var(--btn-white-color);
  box-shadow:
    var(--btn-white-shadow-inner) 0px -2.4px 0px 0px inset,
    rgba(143, 39, 9, 0.2) 0px 1px 3px 0px,
    var(--btn-white-shadow-outer) 0px 0px 0px 1px;
  opacity: 1;

  &:hover {
    background-color: var(--btn-white-hover-color);
    box-shadow:
      var(--btn-white-shadow-inner) 0px 0px 0px 0px inset,
      rgba(143, 39, 9, 0.2) 0px 1px 3px 0px,
      var(--btn-white-shadow-outer) 0px 0px 0px 1px;
    opacity: 1;
  }
}

/* Consolidated base layer */
@layer base {
  html {
    background-color: rgb(249, 249, 249);
  }

  /*
    Border color compatibility for Tailwind v4
    Default border color changed to currentColor, these ensure v3 compatibility
  */
  *,
  ::after,
  ::before,
  ::backdrop,
  ::file-selector-button {
    border-color: var(--color-gray-200, currentColor);
    @apply border-border outline-ring/50;
  }

  body {
    @apply bg-background text-foreground;
  }
}

/* CSS custom properties */
:root {
  --radius: 0.625rem;

  /* Button variables */
  --btn-white-color: rgb(255, 255, 255);
  --btn-white-hover-color: rgb(248, 248, 248);
  --btn-white-shadow-inner: rgb(235, 235, 235);
  --btn-white-shadow-outer: rgb(235, 235, 235);
  --btn-dark-color: rgb(56, 56, 56);
  --btn-dark-hover-color: rgb(92, 92, 92);
  --btn-dark-shadow-inner: rgb(73, 73, 73);
  --btn-dark-shadow-outer: rgb(45, 45, 45);

  /* Color system using OKLCH (modern color format) */
  --background: oklch(1 0 0);
  --foreground: oklch(0.145 0 0);
  --card: oklch(1 0 0);
  --card-foreground: oklch(0.145 0 0);
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(0.145 0 0);
  --primary: oklch(0.205 0 0);
  --primary-foreground: oklch(0.985 0 0);
  --secondary: oklch(0.97 0 0);
  --secondary-foreground: oklch(0.205 0 0);
  --muted: oklch(0.97 0 0);
  --muted-foreground: oklch(0.556 0 0);
  --accent: oklch(0.97 0 0);
  --accent-foreground: oklch(0.205 0 0);
  --destructive: oklch(0.577 0.245 27.325);
  --border: oklch(0.922 0 0);
  --input: oklch(0.922 0 0);
  --ring: oklch(0.708 0 0);
  --chart-1: oklch(0.646 0.222 41.116);
  --chart-2: oklch(0.6 0.118 184.704);
  --chart-3: oklch(0.398 0.07 227.392);
  --chart-4: oklch(0.828 0.189 84.429);
  --chart-5: oklch(0.769 0.188 70.08);
  --sidebar: oklch(0.985 0 0);
  --sidebar-foreground: oklch(0.145 0 0);
  --sidebar-primary: oklch(0.205 0 0);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.97 0 0);
  --sidebar-accent-foreground: oklch(0.205 0 0);
  --sidebar-border: oklch(0.922 0 0);
  --sidebar-ring: oklch(0.708 0 0);
}

.dark {
  --background: oklch(0.145 0 0);
  --foreground: oklch(0.985 0 0);
  --card: oklch(0.205 0 0);
  --card-foreground: oklch(0.985 0 0);
  --popover: oklch(0.205 0 0);
  --popover-foreground: oklch(0.985 0 0);
  --primary: oklch(0.922 0 0);
  --primary-foreground: oklch(0.205 0 0);
  --secondary: oklch(0.269 0 0);
  --secondary-foreground: oklch(0.985 0 0);
  --muted: oklch(0.269 0 0);
  --muted-foreground: oklch(0.708 0 0);
  --accent: oklch(0.269 0 0);
  --accent-foreground: oklch(0.985 0 0);
  --destructive: oklch(0.704 0.191 22.216);
  --border: oklch(1 0 0 / 10%);
  --input: oklch(1 0 0 / 15%);
  --ring: oklch(0.556 0 0);
  --chart-1: oklch(0.488 0.243 264.376);
  --chart-2: oklch(0.696 0.17 162.48);
  --chart-3: oklch(0.769 0.188 70.08);
  --chart-4: oklch(0.627 0.265 303.9);
  --chart-5: oklch(0.645 0.246 16.439);
  --sidebar: oklch(0.205 0 0);
  --sidebar-foreground: oklch(0.985 0 0);
  --sidebar-primary: oklch(0.488 0.243 264.376);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.269 0 0);
  --sidebar-accent-foreground: oklch(0.985 0 0);
  --sidebar-border: oklch(1 0 0 / 10%);
  --sidebar-ring: oklch(0.556 0 0);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
  }
}