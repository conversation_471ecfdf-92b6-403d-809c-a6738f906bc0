import type { NextRequest } from 'next/server';
import { NextResponse } from 'next/server';
import { supabaseClient } from '@/lib/supabase/auth/client';

// GET /api/priorities - Get all priority options
export async function GET(_request: NextRequest) {
  try {
    const { data, error } = await supabaseClient
      .from('priorities')
      .select('*')
      .order('sort_order', { ascending: true });

    if (error) {
      console.error('Error fetching priority options:', error);
      return NextResponse.json(
        { error: 'Failed to fetch priority options' },
        { status: 500 }
      );
    }

    return NextResponse.json({ priorities: data });
  } catch (error) {
    console.error('Unexpected error in GET /api/priorities:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
