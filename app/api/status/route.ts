import type { NextRequest } from 'next/server';
import { NextResponse } from 'next/server';
import { supabaseClient } from '@/lib/supabase/auth/client';

// GET /api/status - Get all status options
export async function GET(_request: NextRequest) {
  try {
    const { data, error } = await supabaseClient
      .from('status')
      .select('*')
      .order('sort_order', { ascending: true });

    if (error) {
      console.error('Error fetching status options:', error);
      return NextResponse.json(
        { error: 'Failed to fetch status options' },
        { status: 500 }
      );
    }

    return NextResponse.json({ status: data });
  } catch (error) {
    console.error('Unexpected error in GET /api/status:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
