import type { NextRequest } from 'next/server';
import { NextResponse } from 'next/server';
import { supabaseClient } from '@/lib/supabase/auth/client';
import type { CreateIssueInput } from '@/lib/supabase/database-modules';

// GET /api/issues - Fetch all issues with optional filtering
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const status = searchParams.get('status');
    const assignee = searchParams.get('assignee');
    const priority = searchParams.get('priority');
    const project = searchParams.get('project');
    const search = searchParams.get('search');

    let query = supabaseClient.from('issues').select(`
      *,
      status:status_id (
        id,
        name,
        color,
        icon_name,
        sort_order
      ),
      assignee:assignee_id (
        id,
        name,
        email,
        avatar_url,
        status,
        role,
        joined_date
      ),
      priority:priority_id (
        id,
        name,
        icon_name,
        sort_order
      ),
      project:project_id (
        id,
        name,
        description,
        icon,
        percent_complete,
        start_date,
        target_date,
        lead_id,
        status_id,
        priority_id,
        health_id
      ),
      cycle:cycle_id (
        id,
        number,
        name,
        team_id,
        start_date,
        end_date,
        progress
      ),
      issue_labels (
        label:label_id (
          id,
          name,
          color
        )
      )
    `);

    // Apply filters
    if (status) {
      query = query.eq('status_id', status);
    }
    if (assignee) {
      if (assignee === 'unassigned') {
        query = query.is('assignee_id', null);
      } else {
        query = query.eq('assignee_id', assignee);
      }
    }
    if (priority) {
      query = query.eq('priority_id', priority);
    }
    if (project) {
      query = query.eq('project_id', project);
    }
    if (search) {
      query = query.or(
        `title.ilike.%${search}%,description.ilike.%${search}%,identifier.ilike.%${search}%`
      );
    }

    // Order by rank for proper sorting
    query = query.order('rank', { ascending: false });

    const { data, error } = await query;

    if (error) {
      console.error('Error fetching issues:', error);
      return NextResponse.json(
        { error: 'Failed to fetch issues' },
        { status: 500 }
      );
    }

    // Transform the data to match the expected format
    const transformedIssues =
      data?.map((item) => ({
        ...item,
        labels:
          item.issue_labels?.map((il: { label: unknown }) => il.label) || [],
        subissues: [], // TODO: Implement subissues query if needed
      })) || [];

    return NextResponse.json({ issues: transformedIssues });
  } catch (error) {
    console.error('Unexpected error in GET /api/issues:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// POST /api/issues - Create a new issue
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const issueInput: CreateIssueInput = body;

    // Get current user from auth
    const {
      data: { user },
      error: authError,
    } = await supabaseClient.auth.getUser();

    if (authError || !user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Generate unique identifier
    const identifier = `ISSUE-${Date.now()}`;

    const newIssue = {
      ...issueInput,
      identifier,
      created_by: user.id,
    };

    // Insert the issue
    const { data, error } = await supabaseClient
      .from('issues')
      .insert([newIssue])
      .select()
      .single();

    if (error) {
      console.error('Error creating issue:', error);
      return NextResponse.json(
        { error: 'Failed to create issue' },
        { status: 500 }
      );
    }

    return NextResponse.json({ issue: data }, { status: 201 });
  } catch (error) {
    console.error('Unexpected error in POST /api/issues:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
