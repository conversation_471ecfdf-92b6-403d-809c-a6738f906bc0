import type { NextRequest } from 'next/server';
import { NextResponse } from 'next/server';
import { supabaseClient } from '@/lib/supabase/auth/client';

// GET /api/issues/[id] - Get a specific issue
export async function GET(
  _request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;

    const { data, error } = await supabaseClient
      .from('issues')
      .select(
        `
        *,
        status:status_id (
          id,
          name,
          color,
          icon_name,
          sort_order
        ),
        assignee:assignee_id (
          id,
          name,
          email,
          avatar_url,
          status,
          role,
          joined_date
        ),
        priority:priority_id (
          id,
          name,
          icon_name,
          sort_order
        ),
        project:project_id (
          id,
          name,
          description,
          icon,
          percent_complete,
          start_date,
          target_date,
          lead_id,
          status_id,
          priority_id,
          health_id
        ),
        cycle:cycle_id (
          id,
          number,
          name,
          team_id,
          start_date,
          end_date,
          progress
        ),
        issue_labels (
          label:label_id (
            id,
            name,
            color
          )
        )
      `
      )
      .eq('id', id)
      .single();

    if (error) {
      if (error.code === 'PGRST116') {
        return NextResponse.json({ error: 'Issue not found' }, { status: 404 });
      }
      console.error('Error fetching issue:', error);
      return NextResponse.json(
        { error: 'Failed to fetch issue' },
        { status: 500 }
      );
    }

    // Transform the data
    const transformedIssue = {
      ...data,
      labels:
        data.issue_labels?.map((il: { label: unknown }) => il.label) || [],
      subissues: [],
    };

    return NextResponse.json({ issue: transformedIssue });
  } catch (error) {
    console.error('Unexpected error in GET /api/issues/[id]:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// PUT /api/issues/[id] - Update a specific issue
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    const body = await request.json();

    // Get current user from auth
    const {
      data: { user },
      error: authError,
    } = await supabaseClient.auth.getUser();

    if (authError || !user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Update the issue
    const { data, error } = await supabaseClient
      .from('issues')
      .update(body)
      .eq('id', id)
      .select()
      .single();

    if (error) {
      if (error.code === 'PGRST116') {
        return NextResponse.json({ error: 'Issue not found' }, { status: 404 });
      }
      console.error('Error updating issue:', error);
      return NextResponse.json(
        { error: 'Failed to update issue' },
        { status: 500 }
      );
    }

    return NextResponse.json({ issue: data });
  } catch (error) {
    console.error('Unexpected error in PUT /api/issues/[id]:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// DELETE /api/issues/[id] - Delete a specific issue
export async function DELETE(
  _request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;

    // Get current user from auth
    const {
      data: { user },
      error: authError,
    } = await supabaseClient.auth.getUser();

    if (authError || !user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Delete the issue
    const { error } = await supabaseClient.from('issues').delete().eq('id', id);

    if (error) {
      console.error('Error deleting issue:', error);
      return NextResponse.json(
        { error: 'Failed to delete issue' },
        { status: 500 }
      );
    }

    return NextResponse.json({ message: 'Issue deleted successfully' });
  } catch (error) {
    console.error('Unexpected error in DELETE /api/issues/[id]:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
