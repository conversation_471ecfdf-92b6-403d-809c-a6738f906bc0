import type { NextRequest } from 'next/server';
import { NextResponse } from 'next/server';
import { supabaseClient } from '@/lib/supabase/auth/client';

// GET /api/labels - Get all label options
export async function GET(_request: NextRequest) {
  try {
    const { data, error } = await supabaseClient
      .from('labels')
      .select('*')
      .order('name', { ascending: true });

    if (error) {
      console.error('Error fetching label options:', error);
      return NextResponse.json(
        { error: 'Failed to fetch label options' },
        { status: 500 }
      );
    }

    return NextResponse.json({ labels: data });
  } catch (error) {
    console.error('Unexpected error in GET /api/labels:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
