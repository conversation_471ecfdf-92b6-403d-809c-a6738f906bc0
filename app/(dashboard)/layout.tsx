import { AppSidebar } from '@/components/layouts/sidebar/app-sidebar';
import {
  SidebarInset,
  SidebarProvider,
  SidebarTrigger,
} from '@/components/ui/sidebar';

export default function DashboardLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <SidebarProvider className='bg-sidebar'>
      <AppSidebar />
      <SidebarInset className='my-2 mr-2 border border-neutral-300 dark:border-neutral-800 relative'>
        <div className='flex items-center absolute top-2 left-2'>
          <SidebarTrigger className='' />
        </div>
        <div className='flex flex-1 flex-col gap-4 pt-0'>{children}</div>
      </SidebarInset>
    </SidebarProvider>
  );
}
